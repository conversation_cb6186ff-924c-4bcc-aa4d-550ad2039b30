import { spawn, ChildProcess } from 'child_process';
import * as path from 'path';
import * as fs from 'fs';
import axios from 'axios';
import { ConfigManager } from '../config/ConfigManager';
import { Logger } from '../../shared/Logger';

export interface BackendStatus {
  running: boolean;
  port: number;
  pid?: number;
  startTime?: Date;
  error?: string;
}

export class BackendManager {
  private process: ChildProcess | null = null;
  private status: BackendStatus;
  private configManager: ConfigManager;
  private logger: Logger;
  private readonly DEFAULT_PORT = 8000;
  private readonly HEALTH_CHECK_INTERVAL = 30000; // 30 seconds
  private healthCheckTimer: NodeJS.Timeout | null = null;

  constructor(configManager: ConfigManager) {
    this.configManager = configManager;
    this.logger = new Logger('BackendManager');
    this.status = {
      running: false,
      port: this.DEFAULT_PORT
    };
  }

  async start(): Promise<void> {
    if (this.status.running) {
      this.logger.warn('Backend is already running');
      return;
    }

    try {
      const port = this.configManager.get('backendPort', this.DEFAULT_PORT);
      const backendPath = this.getBackendPath();
      
      if (!fs.existsSync(backendPath)) {
        throw new Error(`Backend path not found: ${backendPath}`);
      }

      // Check if port is available
      const isPortAvailable = await this.checkPortAvailable(port);
      if (!isPortAvailable) {
        // Try to connect to existing backend
        const isBackendRunning = await this.checkBackendHealth(port);
        if (isBackendRunning) {
          this.logger.info(`Backend already running on port ${port}`);
          this.status = {
            running: true,
            port,
            startTime: new Date()
          };
          this.startHealthCheck();
          return;
        } else {
          throw new Error(`Port ${port} is occupied by another process`);
        }
      }

      await this.startBackendProcess(backendPath, port);
      this.startHealthCheck();
      
    } catch (error) {
      this.logger.error('Failed to start backend:', error);
      this.status.error = error instanceof Error ? error.message : 'Unknown error';
      throw error;
    }
  }

  async stop(): Promise<void> {
    if (this.healthCheckTimer) {
      clearInterval(this.healthCheckTimer);
      this.healthCheckTimer = null;
    }

    if (this.process) {
      this.logger.info('Stopping backend process...');
      this.process.kill('SIGTERM');
      
      // Wait for graceful shutdown
      await new Promise<void>((resolve) => {
        const timeout = setTimeout(() => {
          if (this.process) {
            this.process.kill('SIGKILL');
          }
          resolve();
        }, 5000);

        this.process!.on('exit', () => {
          clearTimeout(timeout);
          resolve();
        });
      });

      this.process = null;
    }

    this.status = {
      running: false,
      port: this.DEFAULT_PORT
    };

    this.logger.info('Backend stopped');
  }

  async restart(): Promise<void> {
    await this.stop();
    await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second
    await this.start();
  }

  getStatus(): BackendStatus {
    return { ...this.status };
  }

  private async startBackendProcess(backendPath: string, port: number): Promise<void> {
    return new Promise((resolve, reject) => {
      const pythonPath = this.getPythonPath();
      const env = {
        ...process.env,
        PORT: port.toString(),
        PYTHONPATH: backendPath,
        DATABASE_URL: this.configManager.get('databaseUrl', 'sqlite:///./entryalert.db'),
        REDIS_URL: this.configManager.get('redisUrl', 'redis://localhost:6379'),
        SECRET_KEY: this.configManager.get('secretKey', 'your-secret-key-here'),
        ALPHA_VANTAGE_API_KEY: this.configManager.get('alphaVantageApiKey', ''),
        YAHOO_FINANCE_API_KEY: this.configManager.get('yahooFinanceApiKey', '')
      };

      this.logger.info(`Starting backend process: ${pythonPath} -m uvicorn app.main:app --host 0.0.0.0 --port ${port}`);

      this.process = spawn(pythonPath, [
        '-m', 'uvicorn',
        'app.main:app',
        '--host', '0.0.0.0',
        '--port', port.toString()
      ], {
        cwd: backendPath,
        env,
        stdio: ['pipe', 'pipe', 'pipe']
      });

      this.process.stdout?.on('data', (data) => {
        this.logger.debug(`Backend stdout: ${data}`);
      });

      this.process.stderr?.on('data', (data) => {
        this.logger.error(`Backend stderr: ${data}`);
      });

      this.process.on('error', (error) => {
        this.logger.error('Backend process error:', error);
        this.status.error = error.message;
        reject(error);
      });

      this.process.on('exit', (code, signal) => {
        this.logger.info(`Backend process exited with code ${code}, signal ${signal}`);
        this.status.running = false;
        this.process = null;
      });

      // Wait for backend to be ready
      this.waitForBackendReady(port)
        .then(() => {
          this.status = {
            running: true,
            port,
            pid: this.process?.pid,
            startTime: new Date()
          };
          this.logger.info(`Backend started successfully on port ${port}`);
          resolve();
        })
        .catch(reject);
    });
  }

  private async waitForBackendReady(port: number, maxAttempts: number = 30): Promise<void> {
    for (let i = 0; i < maxAttempts; i++) {
      try {
        const isReady = await this.checkBackendHealth(port);
        if (isReady) {
          return;
        }
      } catch (error) {
        // Continue trying
      }
      
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    throw new Error('Backend failed to start within timeout period');
  }

  private async checkBackendHealth(port: number): Promise<boolean> {
    try {
      const response = await axios.get(`http://localhost:${port}/health`, {
        timeout: 5000
      });
      return response.status === 200;
    } catch (error) {
      return false;
    }
  }

  private async checkPortAvailable(port: number): Promise<boolean> {
    return new Promise((resolve) => {
      const server = require('net').createServer();
      
      server.listen(port, () => {
        server.close(() => {
          resolve(true);
        });
      });
      
      server.on('error', () => {
        resolve(false);
      });
    });
  }

  private getBackendPath(): string {
    if (process.env.NODE_ENV === 'development') {
      return path.join(__dirname, '../../../../backend');
    } else {
      return path.join(process.resourcesPath, 'backend');
    }
  }

  private getPythonPath(): string {
    // Try to find Python executable
    const possiblePaths = [
      'python3',
      'python',
      '/usr/bin/python3',
      '/usr/local/bin/python3',
      'C:\\Python39\\python.exe',
      'C:\\Python310\\python.exe',
      'C:\\Python311\\python.exe'
    ];

    for (const pythonPath of possiblePaths) {
      try {
        require('child_process').execSync(`${pythonPath} --version`, { stdio: 'ignore' });
        return pythonPath;
      } catch (error) {
        continue;
      }
    }

    throw new Error('Python executable not found. Please install Python 3.9 or higher.');
  }

  private startHealthCheck(): void {
    this.healthCheckTimer = setInterval(async () => {
      if (this.status.running) {
        const isHealthy = await this.checkBackendHealth(this.status.port);
        if (!isHealthy) {
          this.logger.warn('Backend health check failed');
          this.status.running = false;
          this.status.error = 'Backend became unresponsive';
        }
      }
    }, this.HEALTH_CHECK_INTERVAL);
  }
}
