import * as fs from 'fs';
import * as path from 'path';
import { app } from 'electron';
import { ConfigManager } from '../config/ConfigManager';
import { Logger } from '../../shared/Logger';

export interface CachedData {
  timestamp: number;
  data: any;
  expiresAt: number;
}

export interface OfflineStock {
  symbol: string;
  name: string;
  price: number;
  change: number;
  changePercent: number;
  volume: number;
  marketCap?: number;
  lastUpdated: number;
}

export interface OfflineWatchlist {
  id: number;
  name: string;
  stocks: string[];
  lastUpdated: number;
}

export class OfflineDataManager {
  private configManager: ConfigManager;
  private logger: Logger;
  private dataPath: string;
  private cache: Map<string, CachedData> = new Map();
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

  constructor(configManager: ConfigManager) {
    this.configManager = configManager;
    this.logger = new Logger('OfflineDataManager');
    this.dataPath = path.join(app.getPath('userData'), 'offline-data');
    
    this.initializeStorage();
    this.loadCacheFromDisk();
  }

  private initializeStorage(): void {
    try {
      if (!fs.existsSync(this.dataPath)) {
        fs.mkdirSync(this.dataPath, { recursive: true });
      }
      this.logger.info(`Offline data storage initialized at: ${this.dataPath}`);
    } catch (error) {
      this.logger.error('Failed to initialize offline storage:', error);
    }
  }

  private loadCacheFromDisk(): void {
    try {
      const cacheFile = path.join(this.dataPath, 'cache.json');
      if (fs.existsSync(cacheFile)) {
        const cacheData = JSON.parse(fs.readFileSync(cacheFile, 'utf-8'));
        
        // Load cache and remove expired entries
        const now = Date.now();
        for (const [key, value] of Object.entries(cacheData)) {
          const cachedItem = value as CachedData;
          if (cachedItem.expiresAt > now) {
            this.cache.set(key, cachedItem);
          }
        }
        
        this.logger.info(`Loaded ${this.cache.size} cached items from disk`);
      }
    } catch (error) {
      this.logger.error('Failed to load cache from disk:', error);
    }
  }

  private saveCacheToDisk(): void {
    try {
      const cacheFile = path.join(this.dataPath, 'cache.json');
      const cacheObject = Object.fromEntries(this.cache);
      fs.writeFileSync(cacheFile, JSON.stringify(cacheObject, null, 2));
    } catch (error) {
      this.logger.error('Failed to save cache to disk:', error);
    }
  }

  // Generic cache methods
  set(key: string, data: any, ttl: number = this.CACHE_DURATION): void {
    const cachedData: CachedData = {
      timestamp: Date.now(),
      data,
      expiresAt: Date.now() + ttl
    };
    
    this.cache.set(key, cachedData);
    this.saveCacheToDisk();
  }

  get(key: string): any | null {
    const cachedData = this.cache.get(key);
    
    if (!cachedData) {
      return null;
    }
    
    if (cachedData.expiresAt < Date.now()) {
      this.cache.delete(key);
      this.saveCacheToDisk();
      return null;
    }
    
    return cachedData.data;
  }

  has(key: string): boolean {
    const cachedData = this.cache.get(key);
    return cachedData !== undefined && cachedData.expiresAt > Date.now();
  }

  delete(key: string): void {
    this.cache.delete(key);
    this.saveCacheToDisk();
  }

  clear(): void {
    this.cache.clear();
    this.saveCacheToDisk();
  }

  // Stock data caching
  cacheStockData(stocks: OfflineStock[]): void {
    const stocksData = {
      stocks,
      lastUpdated: Date.now()
    };
    
    this.set('stocks:all', stocksData, 10 * 60 * 1000); // 10 minutes
    
    // Cache individual stocks
    stocks.forEach(stock => {
      this.set(`stock:${stock.symbol}`, stock, 5 * 60 * 1000); // 5 minutes
    });
    
    this.logger.info(`Cached ${stocks.length} stocks`);
  }

  getCachedStocks(): OfflineStock[] | null {
    const stocksData = this.get('stocks:all');
    return stocksData ? stocksData.stocks : null;
  }

  getCachedStock(symbol: string): OfflineStock | null {
    return this.get(`stock:${symbol}`);
  }

  // Watchlist caching
  cacheWatchlists(watchlists: OfflineWatchlist[]): void {
    const watchlistsData = {
      watchlists,
      lastUpdated: Date.now()
    };
    
    this.set('watchlists:all', watchlistsData, 30 * 60 * 1000); // 30 minutes
    
    // Cache individual watchlists
    watchlists.forEach(watchlist => {
      this.set(`watchlist:${watchlist.id}`, watchlist, 30 * 60 * 1000);
    });
    
    this.logger.info(`Cached ${watchlists.length} watchlists`);
  }

  getCachedWatchlists(): OfflineWatchlist[] | null {
    const watchlistsData = this.get('watchlists:all');
    return watchlistsData ? watchlistsData.watchlists : null;
  }

  getCachedWatchlist(id: number): OfflineWatchlist | null {
    return this.get(`watchlist:${id}`);
  }

  // Market data caching
  cacheMarketStatus(status: any): void {
    this.set('market:status', status, 60 * 1000); // 1 minute
  }

  getCachedMarketStatus(): any | null {
    return this.get('market:status');
  }

  cacheMarketOverview(overview: any): void {
    this.set('market:overview', overview, 5 * 60 * 1000); // 5 minutes
  }

  getCachedMarketOverview(): any | null {
    return this.get('market:overview');
  }

  // User preferences caching
  cacheUserPreferences(preferences: any): void {
    this.set('user:preferences', preferences, 24 * 60 * 60 * 1000); // 24 hours
  }

  getCachedUserPreferences(): any | null {
    return this.get('user:preferences');
  }

  // Search history caching
  cacheSearchHistory(history: string[]): void {
    this.set('search:history', history, 7 * 24 * 60 * 60 * 1000); // 7 days
  }

  getCachedSearchHistory(): string[] | null {
    return this.get('search:history') || [];
  }

  addToSearchHistory(query: string): void {
    const history = this.getCachedSearchHistory() || [];
    
    // Remove if already exists
    const filteredHistory = history.filter(item => item !== query);
    
    // Add to beginning
    filteredHistory.unshift(query);
    
    // Keep only last 50 searches
    const limitedHistory = filteredHistory.slice(0, 50);
    
    this.cacheSearchHistory(limitedHistory);
  }

  // Persistent storage for critical data
  saveToFile(filename: string, data: any): void {
    try {
      const filePath = path.join(this.dataPath, filename);
      fs.writeFileSync(filePath, JSON.stringify(data, null, 2));
      this.logger.debug(`Saved data to file: ${filename}`);
    } catch (error) {
      this.logger.error(`Failed to save data to file ${filename}:`, error);
    }
  }

  loadFromFile(filename: string): any | null {
    try {
      const filePath = path.join(this.dataPath, filename);
      if (fs.existsSync(filePath)) {
        const data = fs.readFileSync(filePath, 'utf-8');
        return JSON.parse(data);
      }
      return null;
    } catch (error) {
      this.logger.error(`Failed to load data from file ${filename}:`, error);
      return null;
    }
  }

  // Cleanup expired cache entries
  cleanupExpiredCache(): void {
    const now = Date.now();
    let removedCount = 0;
    
    for (const [key, cachedData] of this.cache.entries()) {
      if (cachedData.expiresAt < now) {
        this.cache.delete(key);
        removedCount++;
      }
    }
    
    if (removedCount > 0) {
      this.saveCacheToDisk();
      this.logger.info(`Cleaned up ${removedCount} expired cache entries`);
    }
  }

  // Get cache statistics
  getCacheStats(): {
    totalEntries: number;
    totalSize: number;
    expiredEntries: number;
  } {
    const now = Date.now();
    let expiredEntries = 0;
    let totalSize = 0;
    
    for (const [key, cachedData] of this.cache.entries()) {
      if (cachedData.expiresAt < now) {
        expiredEntries++;
      }
      totalSize += JSON.stringify(cachedData).length;
    }
    
    return {
      totalEntries: this.cache.size,
      totalSize,
      expiredEntries
    };
  }

  // Initialize periodic cleanup
  startPeriodicCleanup(): void {
    // Clean up every 10 minutes
    setInterval(() => {
      this.cleanupExpiredCache();
    }, 10 * 60 * 1000);
  }
}
