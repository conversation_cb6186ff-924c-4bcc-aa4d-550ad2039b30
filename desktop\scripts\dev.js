#!/usr/bin/env node

const { spawn, execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const net = require('net');

class DevManager {
  constructor() {
    this.rootDir = path.join(__dirname, '..');
    this.frontendDir = path.join(this.rootDir, '..', 'frontend');
    this.backendDir = path.join(this.rootDir, '..', 'backend');
    this.processes = [];
  }

  log(message) {
    console.log(`[DEV] ${message}`);
  }

  error(message) {
    console.error(`[ERROR] ${message}`);
  }

  async checkPort(port) {
    return new Promise((resolve) => {
      const server = net.createServer();
      server.listen(port, () => {
        server.close(() => resolve(true));
      });
      server.on('error', () => resolve(false));
    });
  }

  async waitForPort(port, timeout = 30000) {
    const startTime = Date.now();
    
    while (Date.now() - startTime < timeout) {
      try {
        const available = await this.checkPort(port);
        if (!available) {
          return true; // Port is in use (service is running)
        }
        await new Promise(resolve => setTimeout(resolve, 1000));
      } catch (error) {
        // Continue waiting
      }
    }
    
    throw new Error(`Service on port ${port} did not start within ${timeout}ms`);
  }

  spawnProcess(command, args, options = {}) {
    const process = spawn(command, args, {
      stdio: 'inherit',
      shell: true,
      ...options
    });

    process.on('error', (error) => {
      this.error(`Process error: ${error.message}`);
    });

    process.on('exit', (code, signal) => {
      if (code !== 0 && signal !== 'SIGTERM') {
        this.error(`Process exited with code ${code}, signal ${signal}`);
      }
    });

    this.processes.push(process);
    return process;
  }

  async startBackend() {
    this.log('Starting backend server...');
    
    // Check if Python is available
    try {
      execSync('python --version', { stdio: 'ignore' });
    } catch (error) {
      try {
        execSync('python3 --version', { stdio: 'ignore' });
      } catch (error) {
        throw new Error('Python not found. Please install Python 3.9 or higher.');
      }
    }

    // Check if virtual environment exists
    const venvPath = path.join(this.backendDir, 'venv');
    if (!fs.existsSync(venvPath)) {
      this.log('Creating Python virtual environment...');
      execSync('python -m venv venv', { cwd: this.backendDir, stdio: 'inherit' });
    }

    // Install dependencies
    this.log('Installing backend dependencies...');
    const pipCommand = process.platform === 'win32' 
      ? path.join(venvPath, 'Scripts', 'pip')
      : path.join(venvPath, 'bin', 'pip');
    
    execSync(`${pipCommand} install -r requirements.txt`, { 
      cwd: this.backendDir, 
      stdio: 'inherit' 
    });

    // Start backend server
    const pythonCommand = process.platform === 'win32'
      ? path.join(venvPath, 'Scripts', 'python')
      : path.join(venvPath, 'bin', 'python');

    const backendProcess = this.spawnProcess(pythonCommand, [
      '-m', 'uvicorn',
      'app.main:app',
      '--host', '0.0.0.0',
      '--port', '8000',
      '--reload'
    ], {
      cwd: this.backendDir,
      env: {
        ...process.env,
        PYTHONPATH: this.backendDir,
        DATABASE_URL: 'sqlite:///./entryalert.db',
        REDIS_URL: 'redis://localhost:6379',
        SECRET_KEY: 'dev-secret-key-change-in-production',
        DEBUG: 'true'
      }
    });

    // Wait for backend to be ready
    await this.waitForPort(8000);
    this.log('Backend server started on http://localhost:8000');
    
    return backendProcess;
  }

  async startFrontend() {
    this.log('Starting frontend development server...');
    
    // Install frontend dependencies
    this.log('Installing frontend dependencies...');
    execSync('npm install', { cwd: this.frontendDir, stdio: 'inherit' });

    // Start frontend dev server
    const frontendProcess = this.spawnProcess('npm', ['run', 'dev'], {
      cwd: this.frontendDir,
      env: {
        ...process.env,
        VITE_API_URL: 'http://localhost:8000/api/v1',
        VITE_WS_URL: 'ws://localhost:8000/ws'
      }
    });

    // Wait for frontend to be ready
    await this.waitForPort(3000);
    this.log('Frontend server started on http://localhost:3000');
    
    return frontendProcess;
  }

  async startElectron() {
    this.log('Starting Electron application...');
    
    // Install desktop dependencies
    this.log('Installing desktop dependencies...');
    execSync('npm install', { cwd: this.rootDir, stdio: 'inherit' });

    // Build main process
    this.log('Building main process...');
    execSync('npm run build:main', { cwd: this.rootDir, stdio: 'inherit' });

    // Start Electron
    const electronProcess = this.spawnProcess('npm', ['run', 'start'], {
      cwd: this.rootDir,
      env: {
        ...process.env,
        NODE_ENV: 'development'
      }
    });

    this.log('Electron application started');
    return electronProcess;
  }

  async startRedis() {
    this.log('Starting Redis server...');
    
    try {
      // Check if Redis is already running
      const available = await this.checkPort(6379);
      if (!available) {
        this.log('Redis is already running on port 6379');
        return null;
      }

      // Try to start Redis
      const redisProcess = this.spawnProcess('redis-server', [], {
        stdio: 'pipe' // Suppress Redis output
      });

      await this.waitForPort(6379);
      this.log('Redis server started on port 6379');
      
      return redisProcess;
    } catch (error) {
      this.log('Redis not available or failed to start. Some features may not work.');
      return null;
    }
  }

  async startAll() {
    try {
      this.log('Starting EntryAlert development environment...');
      
      // Start Redis (optional)
      await this.startRedis();
      
      // Start backend
      await this.startBackend();
      
      // Start frontend
      await this.startFrontend();
      
      // Wait a bit for services to stabilize
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Start Electron
      await this.startElectron();
      
      this.log('All services started successfully!');
      this.log('Press Ctrl+C to stop all services');
      
    } catch (error) {
      this.error(`Failed to start development environment: ${error.message}`);
      await this.cleanup();
      process.exit(1);
    }
  }

  async cleanup() {
    this.log('Stopping all processes...');
    
    this.processes.forEach(process => {
      if (process && !process.killed) {
        process.kill('SIGTERM');
      }
    });

    // Wait for processes to terminate
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Force kill if necessary
    this.processes.forEach(process => {
      if (process && !process.killed) {
        process.kill('SIGKILL');
      }
    });

    this.processes = [];
    this.log('All processes stopped');
  }

  async startBackendOnly() {
    try {
      await this.startRedis();
      await this.startBackend();
      this.log('Backend development server started');
    } catch (error) {
      this.error(`Failed to start backend: ${error.message}`);
      process.exit(1);
    }
  }

  async startFrontendOnly() {
    try {
      await this.startFrontend();
      this.log('Frontend development server started');
    } catch (error) {
      this.error(`Failed to start frontend: ${error.message}`);
      process.exit(1);
    }
  }
}

// CLI interface
async function main() {
  const args = process.argv.slice(2);
  const command = args[0] || 'all';

  const devManager = new DevManager();

  // Handle process termination
  process.on('SIGINT', async () => {
    console.log('\nReceived SIGINT, shutting down gracefully...');
    await devManager.cleanup();
    process.exit(0);
  });

  process.on('SIGTERM', async () => {
    console.log('\nReceived SIGTERM, shutting down gracefully...');
    await devManager.cleanup();
    process.exit(0);
  });

  try {
    switch (command) {
      case 'all':
        await devManager.startAll();
        break;
      
      case 'backend':
        await devManager.startBackendOnly();
        break;
      
      case 'frontend':
        await devManager.startFrontendOnly();
        break;
      
      case 'electron':
        await devManager.startElectron();
        break;
      
      default:
        console.log(`
Usage: node dev.js [command]

Commands:
  all       - Start all services (default)
  backend   - Start only backend server
  frontend  - Start only frontend server
  electron  - Start only Electron app

Examples:
  node dev.js
  node dev.js all
  node dev.js backend
        `);
        process.exit(1);
    }

    // Keep the process running
    await new Promise(() => {});
    
  } catch (error) {
    console.error('Development script failed:', error);
    await devManager.cleanup();
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = DevManager;
