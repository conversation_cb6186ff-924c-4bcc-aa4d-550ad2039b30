import { app, BrowserWindow, <PERSON>u, Tray, nativeImage, ipcMain, shell, dialog } from 'electron';
import { autoUpdater } from 'electron-updater';
import * as path from 'path';
import * as fs from 'fs';
import { BackendManager } from './backend/BackendManager';
import { NotificationManager } from './notifications/NotificationManager';
import { TrayManager } from './tray/TrayManager';
import { WindowManager } from './window/WindowManager';
import { ConfigManager } from './config/ConfigManager';
import { DesktopApiService } from './services/DesktopApiService';
import { WebSocketService } from './services/WebSocketService';
import { Logger } from '../shared/Logger';

class EntryAlertApp {
  private mainWindow: BrowserWindow | null = null;
  private tray: Tray | null = null;
  private backendManager: BackendManager;
  private notificationManager: NotificationManager;
  private trayManager: TrayManager;
  private windowManager: WindowManager;
  private configManager: ConfigManager;
  private apiService: DesktopApiService;
  private webSocketService: WebSocketService;
  private logger: Logger;

  constructor() {
    this.logger = new Logger('EntryAlertApp');
    this.configManager = new ConfigManager();
    this.backendManager = new BackendManager(this.configManager);
    this.notificationManager = new NotificationManager();
    this.trayManager = new TrayManager();
    this.windowManager = new WindowManager();
    this.apiService = new DesktopApiService(this.configManager);
    this.webSocketService = new WebSocketService(this.configManager, this.notificationManager);

    this.setupApp();
  }

  private setupApp(): void {
    // Handle app ready
    app.whenReady().then(() => {
      this.createWindow();
      this.setupTray();
      this.setupIpcHandlers();
      this.startBackend();
      this.setupAutoUpdater();
    });

    // Handle window closed
    app.on('window-all-closed', () => {
      if (process.platform !== 'darwin') {
        this.cleanup();
        app.quit();
      }
    });

    // Handle app activate (macOS)
    app.on('activate', () => {
      if (BrowserWindow.getAllWindows().length === 0) {
        this.createWindow();
      }
    });

    // Handle before quit
    app.on('before-quit', () => {
      this.cleanup();
    });

    // Handle second instance (single instance lock)
    const gotTheLock = app.requestSingleInstanceLock();
    if (!gotTheLock) {
      app.quit();
    } else {
      app.on('second-instance', () => {
        if (this.mainWindow) {
          if (this.mainWindow.isMinimized()) this.mainWindow.restore();
          this.mainWindow.focus();
        }
      });
    }
  }

  private async createWindow(): Promise<void> {
    this.mainWindow = await this.windowManager.createMainWindow();
    
    // Load the app
    const isDev = process.env.NODE_ENV === 'development';
    if (isDev) {
      this.mainWindow.loadURL('http://localhost:3000');
      this.mainWindow.webContents.openDevTools();
    } else {
      this.mainWindow.loadFile(path.join(__dirname, '../renderer/index.html'));
    }

    // Handle window events
    this.mainWindow.on('closed', () => {
      this.mainWindow = null;
    });

    this.mainWindow.on('minimize', (event: Event) => {
      if (this.configManager.get('minimizeToTray', true)) {
        event.preventDefault();
        this.mainWindow?.hide();
      }
    });

    this.mainWindow.on('close', (event: Event) => {
      if (this.configManager.get('closeToTray', true) && !app.isQuiting) {
        event.preventDefault();
        this.mainWindow?.hide();
      }
    });
  }

  private setupTray(): void {
    this.tray = this.trayManager.createTray();
    
    this.tray.on('click', () => {
      if (this.mainWindow) {
        if (this.mainWindow.isVisible()) {
          this.mainWindow.hide();
        } else {
          this.mainWindow.show();
          this.mainWindow.focus();
        }
      }
    });
  }

  private setupIpcHandlers(): void {
    // Window controls
    ipcMain.handle('window:minimize', () => {
      this.mainWindow?.minimize();
    });

    ipcMain.handle('window:maximize', () => {
      if (this.mainWindow?.isMaximized()) {
        this.mainWindow.unmaximize();
      } else {
        this.mainWindow?.maximize();
      }
    });

    ipcMain.handle('window:close', () => {
      this.mainWindow?.close();
    });

    // App controls
    ipcMain.handle('app:quit', () => {
      app.isQuiting = true;
      app.quit();
    });

    ipcMain.handle('app:version', () => {
      return app.getVersion();
    });

    // Configuration
    ipcMain.handle('config:get', (_, key: string) => {
      return this.configManager.get(key);
    });

    ipcMain.handle('config:set', (_, key: string, value: any) => {
      this.configManager.set(key, value);
    });

    // Notifications
    ipcMain.handle('notification:show', (_, options: any) => {
      this.notificationManager.show(options);
    });

    // External links
    ipcMain.handle('shell:openExternal', (_, url: string) => {
      shell.openExternal(url);
    });

    // Backend status
    ipcMain.handle('backend:status', () => {
      return this.backendManager.getStatus();
    });

    ipcMain.handle('backend:restart', async () => {
      await this.backendManager.restart();
    });

    ipcMain.handle('backend:start', async () => {
      await this.backendManager.start();
    });

    ipcMain.handle('backend:stop', async () => {
      await this.backendManager.stop();
    });

    // Configuration management
    ipcMain.handle('config:getAll', () => {
      return this.configManager.getAll();
    });

    ipcMain.handle('config:setAll', (_, config: any) => {
      this.configManager.setAll(config);
    });

    ipcMain.handle('config:reset', () => {
      this.configManager.reset();
    });

    // Enhanced notifications
    ipcMain.handle('notification:showStockAlert', (_, symbol: string, price: number, change: number, alertType: string) => {
      this.notificationManager.showStockAlert(symbol, price, change, alertType);
    });

    ipcMain.handle('notification:showMarketStatus', (_, status: string) => {
      this.notificationManager.showMarketStatusNotification(status);
    });

    ipcMain.handle('notification:showSystem', (_, title: string, message: string, type?: string) => {
      this.notificationManager.showSystemNotification(title, message, type as any);
    });

    // Dialog handlers
    ipcMain.handle('dialog:selectFile', async (_, options: any) => {
      const result = await dialog.showOpenDialog(this.mainWindow!, {
        properties: ['openFile'],
        ...options
      });
      return result;
    });

    ipcMain.handle('dialog:selectDirectory', async (_, options: any) => {
      const result = await dialog.showOpenDialog(this.mainWindow!, {
        properties: ['openDirectory'],
        ...options
      });
      return result;
    });

    ipcMain.handle('dialog:saveFile', async (_, options: any) => {
      const result = await dialog.showSaveDialog(this.mainWindow!, options);
      return result;
    });

    ipcMain.handle('dialog:showMessageBox', async (_, options: any) => {
      const result = await dialog.showMessageBox(this.mainWindow!, options);
      return result;
    });

    // Shell operations
    ipcMain.handle('shell:showItemInFolder', (_, path: string) => {
      shell.showItemInFolder(path);
    });

    ipcMain.handle('shell:openPath', (_, path: string) => {
      return shell.openPath(path);
    });

    // Store management
    ipcMain.handle('store:get', (_, key: string) => {
      return this.configManager.get(key as any);
    });

    ipcMain.handle('store:set', (_, key: string, value: any) => {
      this.configManager.set(key as any, value);
    });

    ipcMain.handle('store:delete', (_, key: string) => {
      this.configManager.resetKey(key as any);
    });

    ipcMain.handle('store:clear', () => {
      this.configManager.reset();
    });

    ipcMain.handle('store:has', (_, key: string) => {
      return this.configManager.has(key as any);
    });

    // Development utilities
    if (process.env.NODE_ENV === 'development') {
      ipcMain.handle('dev:openDevTools', () => {
        this.mainWindow?.webContents.openDevTools();
      });

      ipcMain.handle('dev:reload', () => {
        this.mainWindow?.webContents.reload();
      });

      ipcMain.handle('dev:toggleDevTools', () => {
        this.mainWindow?.webContents.toggleDevTools();
      });
    }
  }

  private async startBackend(): Promise<void> {
    try {
      await this.backendManager.start();
      this.logger.info('Backend started successfully');

      // Start WebSocket connection after backend is ready
      setTimeout(async () => {
        try {
          await this.webSocketService.connect();
          this.logger.info('WebSocket connected successfully');
        } catch (error) {
          this.logger.error('Failed to connect WebSocket:', error);
        }
      }, 2000);

    } catch (error) {
      this.logger.error('Failed to start backend:', error);
      this.showErrorDialog('Backend Error', 'Failed to start the backend server. Please check your configuration.');
    }
  }

  private setupAutoUpdater(): void {
    if (process.env.NODE_ENV === 'production') {
      autoUpdater.checkForUpdatesAndNotify();
      
      autoUpdater.on('update-available', () => {
        this.notificationManager.show({
          title: 'Update Available',
          body: 'A new version of EntryAlert is available. It will be downloaded in the background.',
          icon: path.join(__dirname, '../assets/icon.png')
        });
      });

      autoUpdater.on('update-downloaded', () => {
        this.notificationManager.show({
          title: 'Update Ready',
          body: 'Update downloaded. Restart the application to apply the update.',
          icon: path.join(__dirname, '../assets/icon.png')
        });
      });
    }
  }

  private showErrorDialog(title: string, content: string): void {
    dialog.showErrorBox(title, content);
  }

  private cleanup(): void {
    this.logger.info('Cleaning up application...');
    this.webSocketService.disconnect();
    this.backendManager.stop();
    this.tray?.destroy();
  }
}

// Create and start the application
new EntryAlertApp();
