import { useEffect, useState, useCallback } from 'react';

// Type definitions for the Electron API
interface ElectronAPI {
  window: {
    minimize: () => Promise<void>;
    maximize: () => Promise<void>;
    close: () => Promise<void>;
    isMaximized: () => Promise<boolean>;
  };
  app: {
    quit: () => Promise<void>;
    getVersion: () => Promise<string>;
    getPlatform: () => string;
    isPackaged: () => boolean;
  };
  config: {
    get: (key: string) => Promise<any>;
    set: (key: string, value: any) => Promise<void>;
    getAll: () => Promise<any>;
    setAll: (config: any) => Promise<void>;
    reset: () => Promise<void>;
  };
  notifications: {
    show: (options: any) => Promise<void>;
    showStockAlert: (symbol: string, price: number, change: number, alertType: string) => Promise<void>;
    showMarketStatus: (status: string) => Promise<void>;
    showSystem: (title: string, message: string, type?: string) => Promise<void>;
  };
  backend: {
    getStatus: () => Promise<any>;
    restart: () => Promise<void>;
    start: () => Promise<void>;
    stop: () => Promise<void>;
  };
  system: {
    openExternal: (url: string) => Promise<void>;
    showItemInFolder: (path: string) => Promise<void>;
    openPath: (path: string) => Promise<void>;
  };
  on: (channel: string, callback: (...args: any[]) => void) => void;
  off: (channel: string, callback: (...args: any[]) => void) => void;
  once: (channel: string, callback: (...args: any[]) => void) => void;
}

// Check if we're running in Electron
export const isElectron = (): boolean => {
  return typeof window !== 'undefined' && window.electronAPI !== undefined;
};

// Get the Electron API
export const getElectronAPI = (): ElectronAPI | null => {
  if (isElectron()) {
    return window.electronAPI;
  }
  return null;
};

// Hook for Electron-specific functionality
export const useElectron = () => {
  const [isElectronApp, setIsElectronApp] = useState(false);
  const [electronAPI, setElectronAPI] = useState<ElectronAPI | null>(null);

  useEffect(() => {
    const electronAvailable = isElectron();
    setIsElectronApp(electronAvailable);
    
    if (electronAvailable) {
      setElectronAPI(window.electronAPI);
    }
  }, []);

  return {
    isElectron: isElectronApp,
    electronAPI
  };
};

// Hook for window controls
export const useWindowControls = () => {
  const { electronAPI } = useElectron();
  const [isMaximized, setIsMaximized] = useState(false);

  const minimize = useCallback(async () => {
    if (electronAPI) {
      await electronAPI.window.minimize();
    }
  }, [electronAPI]);

  const maximize = useCallback(async () => {
    if (electronAPI) {
      await electronAPI.window.maximize();
      const maximized = await electronAPI.window.isMaximized();
      setIsMaximized(maximized);
    }
  }, [electronAPI]);

  const close = useCallback(async () => {
    if (electronAPI) {
      await electronAPI.window.close();
    }
  }, [electronAPI]);

  useEffect(() => {
    if (electronAPI) {
      electronAPI.window.isMaximized().then(setIsMaximized);
    }
  }, [electronAPI]);

  return {
    minimize,
    maximize,
    close,
    isMaximized
  };
};

// Hook for app controls
export const useAppControls = () => {
  const { electronAPI } = useElectron();
  const [version, setVersion] = useState<string>('');
  const [platform, setPlatform] = useState<string>('');
  const [isPackaged, setIsPackaged] = useState<boolean>(false);

  const quit = useCallback(async () => {
    if (electronAPI) {
      await electronAPI.app.quit();
    }
  }, [electronAPI]);

  useEffect(() => {
    if (electronAPI) {
      electronAPI.app.getVersion().then(setVersion);
      setPlatform(electronAPI.app.getPlatform());
      setIsPackaged(electronAPI.app.isPackaged());
    }
  }, [electronAPI]);

  return {
    quit,
    version,
    platform,
    isPackaged
  };
};

// Hook for configuration management
export const useElectronConfig = () => {
  const { electronAPI } = useElectron();
  const [config, setConfig] = useState<any>({});

  const getConfig = useCallback(async (key: string) => {
    if (electronAPI) {
      return await electronAPI.config.get(key);
    }
    return null;
  }, [electronAPI]);

  const setConfigValue = useCallback(async (key: string, value: any) => {
    if (electronAPI) {
      await electronAPI.config.set(key, value);
      // Refresh local config
      const newConfig = await electronAPI.config.getAll();
      setConfig(newConfig);
    }
  }, [electronAPI]);

  const getAllConfig = useCallback(async () => {
    if (electronAPI) {
      const allConfig = await electronAPI.config.getAll();
      setConfig(allConfig);
      return allConfig;
    }
    return {};
  }, [electronAPI]);

  const resetConfig = useCallback(async () => {
    if (electronAPI) {
      await electronAPI.config.reset();
      const newConfig = await electronAPI.config.getAll();
      setConfig(newConfig);
    }
  }, [electronAPI]);

  useEffect(() => {
    getAllConfig();
  }, [getAllConfig]);

  return {
    config,
    getConfig,
    setConfig: setConfigValue,
    getAllConfig,
    resetConfig
  };
};

// Hook for notifications
export const useElectronNotifications = () => {
  const { electronAPI } = useElectron();

  const showNotification = useCallback(async (options: any) => {
    if (electronAPI) {
      await electronAPI.notifications.show(options);
    }
  }, [electronAPI]);

  const showStockAlert = useCallback(async (
    symbol: string,
    price: number,
    change: number,
    alertType: string
  ) => {
    if (electronAPI) {
      await electronAPI.notifications.showStockAlert(symbol, price, change, alertType);
    }
  }, [electronAPI]);

  const showMarketStatus = useCallback(async (status: string) => {
    if (electronAPI) {
      await electronAPI.notifications.showMarketStatus(status);
    }
  }, [electronAPI]);

  const showSystemNotification = useCallback(async (
    title: string,
    message: string,
    type?: string
  ) => {
    if (electronAPI) {
      await electronAPI.notifications.showSystem(title, message, type);
    }
  }, [electronAPI]);

  return {
    showNotification,
    showStockAlert,
    showMarketStatus,
    showSystemNotification
  };
};

// Hook for backend status
export const useBackendStatus = () => {
  const { electronAPI } = useElectron();
  const [status, setStatus] = useState<any>({ running: false });

  const getStatus = useCallback(async () => {
    if (electronAPI) {
      const backendStatus = await electronAPI.backend.getStatus();
      setStatus(backendStatus);
      return backendStatus;
    }
    return { running: false };
  }, [electronAPI]);

  const restart = useCallback(async () => {
    if (electronAPI) {
      await electronAPI.backend.restart();
      // Refresh status after restart
      setTimeout(() => getStatus(), 2000);
    }
  }, [electronAPI, getStatus]);

  const start = useCallback(async () => {
    if (electronAPI) {
      await electronAPI.backend.start();
      setTimeout(() => getStatus(), 1000);
    }
  }, [electronAPI, getStatus]);

  const stop = useCallback(async () => {
    if (electronAPI) {
      await electronAPI.backend.stop();
      setTimeout(() => getStatus(), 1000);
    }
  }, [electronAPI, getStatus]);

  useEffect(() => {
    getStatus();
    
    // Set up periodic status checks
    const interval = setInterval(getStatus, 30000); // Check every 30 seconds
    
    return () => clearInterval(interval);
  }, [getStatus]);

  return {
    status,
    getStatus,
    restart,
    start,
    stop
  };
};

// Hook for system integration
export const useSystemIntegration = () => {
  const { electronAPI } = useElectron();

  const openExternal = useCallback(async (url: string) => {
    if (electronAPI) {
      await electronAPI.system.openExternal(url);
    } else {
      // Fallback for web
      window.open(url, '_blank');
    }
  }, [electronAPI]);

  const showItemInFolder = useCallback(async (path: string) => {
    if (electronAPI) {
      await electronAPI.system.showItemInFolder(path);
    }
  }, [electronAPI]);

  const openPath = useCallback(async (path: string) => {
    if (electronAPI) {
      await electronAPI.system.openPath(path);
    }
  }, [electronAPI]);

  return {
    openExternal,
    showItemInFolder,
    openPath
  };
};
