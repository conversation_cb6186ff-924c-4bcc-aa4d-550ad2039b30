"""
Performance monitoring and metrics collection service.
"""
import time
import asyncio
import psutil
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from collections import defaultdict, deque
import threading
import logging

from ..database import get_redis, SessionLocal
from ..config import settings
from ..utils.logger import get_logger

logger = get_logger(__name__)


class MetricsCollector:
    """Collects and stores performance metrics."""
    
    def __init__(self):
        self.redis = get_redis()
        self.metrics = defaultdict(deque)
        self.lock = threading.Lock()
        
        # Keep metrics for last 24 hours
        self.max_metrics_age = 24 * 60 * 60  # 24 hours in seconds
        self.cleanup_interval = 300  # 5 minutes
        
    def record_metric(self, metric_name: str, value: float, tags: Optional[Dict[str, str]] = None):
        """Record a metric value."""
        try:
            timestamp = time.time()
            metric_data = {
                'value': value,
                'timestamp': timestamp,
                'tags': tags or {}
            }
            
            with self.lock:
                self.metrics[metric_name].append(metric_data)
                
                # Keep only recent metrics
                cutoff_time = timestamp - self.max_metrics_age
                while (self.metrics[metric_name] and 
                       self.metrics[metric_name][0]['timestamp'] < cutoff_time):
                    self.metrics[metric_name].popleft()
            
            # Also store in Redis for persistence
            self._store_metric_in_redis(metric_name, metric_data)
            
        except Exception as e:
            logger.error(f"Error recording metric {metric_name}: {e}")
    
    def get_metrics(self, metric_name: str, duration_minutes: int = 60) -> List[Dict[str, Any]]:
        """Get metrics for the specified duration."""
        try:
            cutoff_time = time.time() - (duration_minutes * 60)
            
            with self.lock:
                return [
                    metric for metric in self.metrics[metric_name]
                    if metric['timestamp'] >= cutoff_time
                ]
                
        except Exception as e:
            logger.error(f"Error getting metrics for {metric_name}: {e}")
            return []
    
    def get_metric_summary(self, metric_name: str, duration_minutes: int = 60) -> Dict[str, Any]:
        """Get summary statistics for a metric."""
        try:
            metrics = self.get_metrics(metric_name, duration_minutes)
            if not metrics:
                return {}
            
            values = [m['value'] for m in metrics]
            
            return {
                'count': len(values),
                'min': min(values),
                'max': max(values),
                'avg': sum(values) / len(values),
                'latest': values[-1] if values else None,
                'duration_minutes': duration_minutes
            }
            
        except Exception as e:
            logger.error(f"Error getting metric summary for {metric_name}: {e}")
            return {}
    
    def _store_metric_in_redis(self, metric_name: str, metric_data: Dict[str, Any]):
        """Store metric in Redis for persistence."""
        try:
            key = f"metrics:{metric_name}:{int(metric_data['timestamp'])}"
            self.redis.setex(key, 86400, str(metric_data))  # 24 hour TTL
        except Exception as e:
            logger.error(f"Error storing metric in Redis: {e}")


class PerformanceMonitor:
    """Main performance monitoring service."""
    
    def __init__(self):
        self.metrics_collector = MetricsCollector()
        self.start_time = time.time()
        
    def record_api_request(self, endpoint: str, method: str, duration: float, status_code: int):
        """Record API request metrics."""
        tags = {
            'endpoint': endpoint,
            'method': method,
            'status_code': str(status_code)
        }
        
        self.metrics_collector.record_metric('api_request_duration', duration, tags)
        self.metrics_collector.record_metric('api_request_count', 1, tags)
    
    def record_background_task(self, task_name: str, duration: float, success: bool):
        """Record background task metrics."""
        tags = {
            'task_name': task_name,
            'success': str(success)
        }
        
        self.metrics_collector.record_metric('background_task_duration', duration, tags)
        self.metrics_collector.record_metric('background_task_count', 1, tags)
    
    def record_database_query(self, query_type: str, duration: float, table: str = None):
        """Record database query metrics."""
        tags = {
            'query_type': query_type,
            'table': table or 'unknown'
        }
        
        self.metrics_collector.record_metric('db_query_duration', duration, tags)
        self.metrics_collector.record_metric('db_query_count', 1, tags)
    
    def record_cache_operation(self, operation: str, hit: bool, duration: float = None):
        """Record cache operation metrics."""
        tags = {
            'operation': operation,
            'hit': str(hit)
        }
        
        self.metrics_collector.record_metric('cache_operation_count', 1, tags)
        if duration is not None:
            self.metrics_collector.record_metric('cache_operation_duration', duration, tags)
    
    def record_market_data_fetch(self, provider: str, symbol: str, duration: float, success: bool):
        """Record market data fetch metrics."""
        tags = {
            'provider': provider,
            'symbol': symbol,
            'success': str(success)
        }
        
        self.metrics_collector.record_metric('market_data_fetch_duration', duration, tags)
        self.metrics_collector.record_metric('market_data_fetch_count', 1, tags)
    
    def get_system_metrics(self) -> Dict[str, Any]:
        """Get current system metrics."""
        try:
            # CPU and Memory
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            # Database connections
            db_connections = self._get_database_connections()
            
            # Application uptime
            uptime_seconds = time.time() - self.start_time
            
            return {
                'system': {
                    'cpu_percent': cpu_percent,
                    'memory_percent': memory.percent,
                    'memory_used_gb': memory.used / (1024**3),
                    'memory_total_gb': memory.total / (1024**3),
                    'disk_percent': disk.percent,
                    'disk_used_gb': disk.used / (1024**3),
                    'disk_total_gb': disk.total / (1024**3)
                },
                'application': {
                    'uptime_seconds': uptime_seconds,
                    'uptime_hours': uptime_seconds / 3600,
                    'database_connections': db_connections
                },
                'timestamp': datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error getting system metrics: {e}")
            return {}
    
    def get_performance_summary(self, duration_minutes: int = 60) -> Dict[str, Any]:
        """Get performance summary for the specified duration."""
        try:
            return {
                'api_requests': self.metrics_collector.get_metric_summary('api_request_duration', duration_minutes),
                'background_tasks': self.metrics_collector.get_metric_summary('background_task_duration', duration_minutes),
                'database_queries': self.metrics_collector.get_metric_summary('db_query_duration', duration_minutes),
                'cache_operations': self.metrics_collector.get_metric_summary('cache_operation_duration', duration_minutes),
                'market_data_fetches': self.metrics_collector.get_metric_summary('market_data_fetch_duration', duration_minutes),
                'system': self.get_system_metrics(),
                'duration_minutes': duration_minutes
            }
            
        except Exception as e:
            logger.error(f"Error getting performance summary: {e}")
            return {}
    
    def get_api_endpoint_stats(self, duration_minutes: int = 60) -> Dict[str, Any]:
        """Get API endpoint performance statistics."""
        try:
            metrics = self.metrics_collector.get_metrics('api_request_duration', duration_minutes)
            
            endpoint_stats = defaultdict(lambda: {'count': 0, 'total_duration': 0, 'durations': []})
            
            for metric in metrics:
                endpoint = metric['tags'].get('endpoint', 'unknown')
                duration = metric['value']
                
                endpoint_stats[endpoint]['count'] += 1
                endpoint_stats[endpoint]['total_duration'] += duration
                endpoint_stats[endpoint]['durations'].append(duration)
            
            # Calculate statistics for each endpoint
            result = {}
            for endpoint, stats in endpoint_stats.items():
                durations = stats['durations']
                result[endpoint] = {
                    'request_count': stats['count'],
                    'avg_duration': stats['total_duration'] / stats['count'],
                    'min_duration': min(durations),
                    'max_duration': max(durations),
                    'total_duration': stats['total_duration']
                }
            
            return result
            
        except Exception as e:
            logger.error(f"Error getting API endpoint stats: {e}")
            return {}
    
    def get_background_task_stats(self, duration_minutes: int = 60) -> Dict[str, Any]:
        """Get background task performance statistics."""
        try:
            duration_metrics = self.metrics_collector.get_metrics('background_task_duration', duration_minutes)
            count_metrics = self.metrics_collector.get_metrics('background_task_count', duration_minutes)
            
            task_stats = defaultdict(lambda: {
                'count': 0, 'success_count': 0, 'failure_count': 0,
                'total_duration': 0, 'durations': []
            })
            
            # Process duration metrics
            for metric in duration_metrics:
                task_name = metric['tags'].get('task_name', 'unknown')
                duration = metric['value']
                success = metric['tags'].get('success', 'true') == 'true'
                
                task_stats[task_name]['total_duration'] += duration
                task_stats[task_name]['durations'].append(duration)
                
                if success:
                    task_stats[task_name]['success_count'] += 1
                else:
                    task_stats[task_name]['failure_count'] += 1
            
            # Process count metrics
            for metric in count_metrics:
                task_name = metric['tags'].get('task_name', 'unknown')
                task_stats[task_name]['count'] += 1
            
            # Calculate statistics
            result = {}
            for task_name, stats in task_stats.items():
                durations = stats['durations']
                if durations:
                    result[task_name] = {
                        'execution_count': stats['count'],
                        'success_count': stats['success_count'],
                        'failure_count': stats['failure_count'],
                        'success_rate': stats['success_count'] / stats['count'] if stats['count'] > 0 else 0,
                        'avg_duration': stats['total_duration'] / len(durations),
                        'min_duration': min(durations),
                        'max_duration': max(durations),
                        'total_duration': stats['total_duration']
                    }
            
            return result
            
        except Exception as e:
            logger.error(f"Error getting background task stats: {e}")
            return {}
    
    def _get_database_connections(self) -> int:
        """Get current database connection count."""
        try:
            db = SessionLocal()
            try:
                result = db.execute("SELECT count(*) FROM pg_stat_activity WHERE state = 'active'")
                return result.scalar()
            finally:
                db.close()
        except Exception as e:
            logger.error(f"Error getting database connections: {e}")
            return 0


# Global performance monitor instance
performance_monitor = PerformanceMonitor()
