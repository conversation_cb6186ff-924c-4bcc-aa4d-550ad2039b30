import React from 'react';
import { Minus, Square, X, Maximize2, Minimize2 } from 'lucide-react';
import { useWindowControls, useElectron } from '../hooks/useElectron';

interface TitleBarProps {
  title?: string;
  showControls?: boolean;
}

export const TitleBar: React.FC<TitleBarProps> = ({ 
  title = 'EntryAlert', 
  showControls = true 
}) => {
  const { isElectron } = useElectron();
  const { minimize, maximize, close, isMaximized } = useWindowControls();

  // Only show custom title bar on Windows and Linux
  if (!isElectron || process.platform === 'darwin') {
    return null;
  }

  return (
    <div className="flex items-center justify-between h-8 bg-gray-900 border-b border-gray-700 select-none">
      {/* App Icon and Title */}
      <div className="flex items-center px-3 space-x-2">
        <img 
          src="/assets/icon-16.png" 
          alt="EntryAlert" 
          className="w-4 h-4"
        />
        <span className="text-sm text-gray-300 font-medium">
          {title}
        </span>
      </div>

      {/* Window Controls */}
      {showControls && (
        <div className="flex">
          <button
            onClick={minimize}
            className="flex items-center justify-center w-12 h-8 text-gray-400 hover:text-white hover:bg-gray-700 transition-colors"
            title="Minimize"
          >
            <Minus size={14} />
          </button>
          
          <button
            onClick={maximize}
            className="flex items-center justify-center w-12 h-8 text-gray-400 hover:text-white hover:bg-gray-700 transition-colors"
            title={isMaximized ? "Restore" : "Maximize"}
          >
            {isMaximized ? <Minimize2 size={14} /> : <Maximize2 size={14} />}
          </button>
          
          <button
            onClick={close}
            className="flex items-center justify-center w-12 h-8 text-gray-400 hover:text-white hover:bg-red-600 transition-colors"
            title="Close"
          >
            <X size={14} />
          </button>
        </div>
      )}
    </div>
  );
};

export default TitleBar;
