import Store from 'electron-store';
import { Logger } from '../../shared/Logger';

export interface AppConfig {
  // Window settings
  windowState?: {
    bounds: {
      x: number;
      y: number;
      width: number;
      height: number;
    };
    isMaximized: boolean;
  };
  minimizeToTray: boolean;
  closeToTray: boolean;
  startMinimized: boolean;
  autoStart: boolean;

  // Backend settings
  backendPort: number;
  databaseUrl: string;
  redisUrl: string;
  secretKey: string;

  // API Keys
  alphaVantageApiKey: string;
  yahooFinanceApiKey: string;

  // Notification settings
  enableNotifications: boolean;
  enableSoundAlerts: boolean;
  notificationDuration: number;

  // Theme and UI
  theme: 'light' | 'dark' | 'system';
  language: string;
  
  // Trading settings
  defaultWatchlistRefreshInterval: number;
  maxConcurrentAlerts: number;
  
  // Performance settings
  enableHardwareAcceleration: boolean;
  maxMemoryUsage: number;
}

const defaultConfig: AppConfig = {
  minimizeToTray: true,
  closeToTray: true,
  startMinimized: false,
  autoStart: false,
  
  backendPort: 8000,
  databaseUrl: 'sqlite:///./entryalert.db',
  redisUrl: 'redis://localhost:6379',
  secretKey: 'your-secret-key-here',
  
  alphaVantageApiKey: '',
  yahooFinanceApiKey: '',
  
  enableNotifications: true,
  enableSoundAlerts: true,
  notificationDuration: 10000,
  
  theme: 'system',
  language: 'en',
  
  defaultWatchlistRefreshInterval: 30000,
  maxConcurrentAlerts: 10,
  
  enableHardwareAcceleration: true,
  maxMemoryUsage: 512
};

export class ConfigManager {
  private store: Store<AppConfig>;
  private logger: Logger;

  constructor() {
    this.logger = new Logger('ConfigManager');
    
    this.store = new Store<AppConfig>({
      name: 'entryalert-config',
      defaults: defaultConfig,
      schema: {
        minimizeToTray: {
          type: 'boolean',
          default: true
        },
        closeToTray: {
          type: 'boolean',
          default: true
        },
        startMinimized: {
          type: 'boolean',
          default: false
        },
        autoStart: {
          type: 'boolean',
          default: false
        },
        backendPort: {
          type: 'number',
          minimum: 1024,
          maximum: 65535,
          default: 8000
        },
        databaseUrl: {
          type: 'string',
          default: 'sqlite:///./entryalert.db'
        },
        redisUrl: {
          type: 'string',
          default: 'redis://localhost:6379'
        },
        secretKey: {
          type: 'string',
          minLength: 10,
          default: 'your-secret-key-here'
        },
        alphaVantageApiKey: {
          type: 'string',
          default: ''
        },
        yahooFinanceApiKey: {
          type: 'string',
          default: ''
        },
        enableNotifications: {
          type: 'boolean',
          default: true
        },
        enableSoundAlerts: {
          type: 'boolean',
          default: true
        },
        notificationDuration: {
          type: 'number',
          minimum: 1000,
          maximum: 30000,
          default: 10000
        },
        theme: {
          type: 'string',
          enum: ['light', 'dark', 'system'],
          default: 'system'
        },
        language: {
          type: 'string',
          default: 'en'
        },
        defaultWatchlistRefreshInterval: {
          type: 'number',
          minimum: 5000,
          maximum: 300000,
          default: 30000
        },
        maxConcurrentAlerts: {
          type: 'number',
          minimum: 1,
          maximum: 50,
          default: 10
        },
        enableHardwareAcceleration: {
          type: 'boolean',
          default: true
        },
        maxMemoryUsage: {
          type: 'number',
          minimum: 128,
          maximum: 4096,
          default: 512
        }
      }
    });

    this.logger.info('Configuration manager initialized');
  }

  get<K extends keyof AppConfig>(key: K): AppConfig[K];
  get<K extends keyof AppConfig>(key: K, defaultValue: AppConfig[K]): AppConfig[K];
  get<K extends keyof AppConfig>(key: K, defaultValue?: AppConfig[K]): AppConfig[K] {
    try {
      const value = this.store.get(key);
      return value !== undefined ? value : (defaultValue ?? defaultConfig[key]);
    } catch (error) {
      this.logger.error(`Failed to get config value for key: ${String(key)}`, error);
      return defaultValue ?? defaultConfig[key];
    }
  }

  set<K extends keyof AppConfig>(key: K, value: AppConfig[K]): void {
    try {
      this.store.set(key, value);
      this.logger.debug(`Config updated: ${String(key)} = ${JSON.stringify(value)}`);
    } catch (error) {
      this.logger.error(`Failed to set config value for key: ${String(key)}`, error);
    }
  }

  getAll(): AppConfig {
    try {
      return this.store.store;
    } catch (error) {
      this.logger.error('Failed to get all config values', error);
      return defaultConfig;
    }
  }

  setAll(config: Partial<AppConfig>): void {
    try {
      Object.entries(config).forEach(([key, value]) => {
        this.store.set(key as keyof AppConfig, value);
      });
      this.logger.info('Configuration updated');
    } catch (error) {
      this.logger.error('Failed to set config values', error);
    }
  }

  reset(): void {
    try {
      this.store.clear();
      this.logger.info('Configuration reset to defaults');
    } catch (error) {
      this.logger.error('Failed to reset configuration', error);
    }
  }

  resetKey<K extends keyof AppConfig>(key: K): void {
    try {
      this.store.delete(key);
      this.logger.debug(`Config key reset: ${String(key)}`);
    } catch (error) {
      this.logger.error(`Failed to reset config key: ${String(key)}`, error);
    }
  }

  has<K extends keyof AppConfig>(key: K): boolean {
    try {
      return this.store.has(key);
    } catch (error) {
      this.logger.error(`Failed to check config key: ${String(key)}`, error);
      return false;
    }
  }

  getConfigPath(): string {
    return this.store.path;
  }

  onDidChange<K extends keyof AppConfig>(
    key: K,
    callback: (newValue: AppConfig[K], oldValue: AppConfig[K]) => void
  ): () => void {
    return this.store.onDidChange(key, callback);
  }

  onDidAnyChange(callback: (newValue: AppConfig, oldValue: AppConfig) => void): () => void {
    return this.store.onDidAnyChange(callback);
  }

  validateConfig(): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    const config = this.getAll();

    // Validate port range
    if (config.backendPort < 1024 || config.backendPort > 65535) {
      errors.push('Backend port must be between 1024 and 65535');
    }

    // Validate secret key
    if (config.secretKey.length < 10) {
      errors.push('Secret key must be at least 10 characters long');
    }

    // Validate notification duration
    if (config.notificationDuration < 1000 || config.notificationDuration > 30000) {
      errors.push('Notification duration must be between 1 and 30 seconds');
    }

    // Validate refresh interval
    if (config.defaultWatchlistRefreshInterval < 5000 || config.defaultWatchlistRefreshInterval > 300000) {
      errors.push('Watchlist refresh interval must be between 5 seconds and 5 minutes');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}
