import * as fs from 'fs';
import * as path from 'path';
import { app } from 'electron';

export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3
}

export interface LogEntry {
  timestamp: Date;
  level: LogLevel;
  module: string;
  message: string;
  data?: any;
}

export class Logger {
  private module: string;
  private static logLevel: LogLevel = LogLevel.INFO;
  private static logFile: string;
  private static initialized = false;

  constructor(module: string) {
    this.module = module;
    
    if (!Logger.initialized) {
      Logger.initialize();
    }
  }

  private static initialize(): void {
    try {
      const userDataPath = app.getPath('userData');
      const logsDir = path.join(userDataPath, 'logs');
      
      // Create logs directory if it doesn't exist
      if (!fs.existsSync(logsDir)) {
        fs.mkdirSync(logsDir, { recursive: true });
      }

      // Set log file path with date
      const today = new Date().toISOString().split('T')[0];
      Logger.logFile = path.join(logsDir, `entryalert-${today}.log`);
      
      // Set log level from environment or config
      const envLogLevel = process.env.LOG_LEVEL?.toLowerCase();
      switch (envLogLevel) {
        case 'debug':
          Logger.logLevel = LogLevel.DEBUG;
          break;
        case 'info':
          Logger.logLevel = LogLevel.INFO;
          break;
        case 'warn':
          Logger.logLevel = LogLevel.WARN;
          break;
        case 'error':
          Logger.logLevel = LogLevel.ERROR;
          break;
        default:
          Logger.logLevel = process.env.NODE_ENV === 'development' ? LogLevel.DEBUG : LogLevel.INFO;
      }

      Logger.initialized = true;
      
      // Log initialization
      const initLogger = new Logger('Logger');
      initLogger.info(`Logger initialized - Level: ${LogLevel[Logger.logLevel]}, File: ${Logger.logFile}`);
      
    } catch (error) {
      console.error('Failed to initialize logger:', error);
      Logger.initialized = true; // Prevent infinite retry
    }
  }

  debug(message: string, data?: any): void {
    this.log(LogLevel.DEBUG, message, data);
  }

  info(message: string, data?: any): void {
    this.log(LogLevel.INFO, message, data);
  }

  warn(message: string, data?: any): void {
    this.log(LogLevel.WARN, message, data);
  }

  error(message: string, data?: any): void {
    this.log(LogLevel.ERROR, message, data);
  }

  private log(level: LogLevel, message: string, data?: any): void {
    if (level < Logger.logLevel) {
      return;
    }

    const entry: LogEntry = {
      timestamp: new Date(),
      level,
      module: this.module,
      message,
      data
    };

    // Console output
    this.logToConsole(entry);
    
    // File output
    this.logToFile(entry);
  }

  private logToConsole(entry: LogEntry): void {
    const timestamp = entry.timestamp.toISOString();
    const levelStr = LogLevel[entry.level].padEnd(5);
    const moduleStr = entry.module.padEnd(15);
    const logMessage = `[${timestamp}] ${levelStr} [${moduleStr}] ${entry.message}`;

    switch (entry.level) {
      case LogLevel.DEBUG:
        console.debug(logMessage, entry.data || '');
        break;
      case LogLevel.INFO:
        console.info(logMessage, entry.data || '');
        break;
      case LogLevel.WARN:
        console.warn(logMessage, entry.data || '');
        break;
      case LogLevel.ERROR:
        console.error(logMessage, entry.data || '');
        break;
    }
  }

  private logToFile(entry: LogEntry): void {
    try {
      const timestamp = entry.timestamp.toISOString();
      const levelStr = LogLevel[entry.level];
      const dataStr = entry.data ? ` | Data: ${JSON.stringify(entry.data)}` : '';
      const logLine = `[${timestamp}] ${levelStr} [${entry.module}] ${entry.message}${dataStr}\n`;

      fs.appendFileSync(Logger.logFile, logLine);
    } catch (error) {
      console.error('Failed to write to log file:', error);
    }
  }

  static setLogLevel(level: LogLevel): void {
    Logger.logLevel = level;
  }

  static getLogLevel(): LogLevel {
    return Logger.logLevel;
  }

  static getLogFile(): string {
    return Logger.logFile;
  }

  static async getRecentLogs(lines: number = 100): Promise<string[]> {
    try {
      if (!fs.existsSync(Logger.logFile)) {
        return [];
      }

      const content = fs.readFileSync(Logger.logFile, 'utf-8');
      const allLines = content.split('\n').filter(line => line.trim());
      
      return allLines.slice(-lines);
    } catch (error) {
      console.error('Failed to read log file:', error);
      return [];
    }
  }

  static async clearLogs(): Promise<void> {
    try {
      if (fs.existsSync(Logger.logFile)) {
        fs.unlinkSync(Logger.logFile);
      }
    } catch (error) {
      console.error('Failed to clear log file:', error);
    }
  }

  static async rotateLogs(maxFiles: number = 7): Promise<void> {
    try {
      const userDataPath = app.getPath('userData');
      const logsDir = path.join(userDataPath, 'logs');
      
      if (!fs.existsSync(logsDir)) {
        return;
      }

      const files = fs.readdirSync(logsDir)
        .filter(file => file.startsWith('entryalert-') && file.endsWith('.log'))
        .map(file => ({
          name: file,
          path: path.join(logsDir, file),
          mtime: fs.statSync(path.join(logsDir, file)).mtime
        }))
        .sort((a, b) => b.mtime.getTime() - a.mtime.getTime());

      // Remove old log files
      if (files.length > maxFiles) {
        const filesToDelete = files.slice(maxFiles);
        for (const file of filesToDelete) {
          fs.unlinkSync(file.path);
        }
      }
    } catch (error) {
      console.error('Failed to rotate logs:', error);
    }
  }
}
