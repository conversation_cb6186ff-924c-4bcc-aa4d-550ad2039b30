import { BrowserWindow, screen } from 'electron';
import * as path from 'path';
import { Logger } from '../../shared/Logger';

export class WindowManager {
  private logger: Logger;

  constructor() {
    this.logger = new Logger('WindowManager');
  }

  async createMainWindow(): Promise<BrowserWindow> {
    const { width, height } = screen.getPrimaryDisplay().workAreaSize;
    
    const window = new BrowserWindow({
      width: Math.min(1400, width - 100),
      height: Math.min(900, height - 100),
      minWidth: 1000,
      minHeight: 700,
      show: false,
      icon: this.getAppIcon(),
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        enableRemoteModule: false,
        preload: path.join(__dirname, '../preload/preload.js'),
        webSecurity: true,
        allowRunningInsecureContent: false,
        experimentalFeatures: false
      },
      titleBarStyle: process.platform === 'darwin' ? 'hiddenInset' : 'default',
      frame: process.platform !== 'win32',
      backgroundColor: '#1a1a1a',
      vibrancy: process.platform === 'darwin' ? 'dark' : undefined
    });

    // Center the window
    window.center();

    // Show window when ready
    window.once('ready-to-show', () => {
      window.show();
      
      if (process.env.NODE_ENV === 'development') {
        window.webContents.openDevTools();
      }
    });

    // Handle window state
    this.setupWindowStateManagement(window);

    this.logger.info('Main window created');
    return window;
  }

  private getAppIcon(): string {
    const iconName = process.platform === 'win32' ? 'icon.ico' : 
                    process.platform === 'darwin' ? 'icon.icns' : 'icon.png';
    return path.join(__dirname, '../../assets', iconName);
  }

  private setupWindowStateManagement(window: BrowserWindow): void {
    // Save window state on resize/move
    const saveWindowState = () => {
      const bounds = window.getBounds();
      const isMaximized = window.isMaximized();
      
      // Save to config (would need ConfigManager instance)
      // this.configManager.set('windowState', { bounds, isMaximized });
    };

    window.on('resize', saveWindowState);
    window.on('move', saveWindowState);
    window.on('maximize', saveWindowState);
    window.on('unmaximize', saveWindowState);
  }

  createSplashWindow(): BrowserWindow {
    const splash = new BrowserWindow({
      width: 400,
      height: 300,
      frame: false,
      alwaysOnTop: true,
      transparent: true,
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true
      }
    });

    splash.loadFile(path.join(__dirname, '../../assets/splash.html'));
    splash.center();

    return splash;
  }

  createAboutWindow(): BrowserWindow {
    const about = new BrowserWindow({
      width: 500,
      height: 400,
      resizable: false,
      minimizable: false,
      maximizable: false,
      modal: true,
      show: false,
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        preload: path.join(__dirname, '../preload/preload.js')
      }
    });

    about.loadFile(path.join(__dirname, '../../assets/about.html'));
    about.once('ready-to-show', () => {
      about.show();
    });

    return about;
  }
}
