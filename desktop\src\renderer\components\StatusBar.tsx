import React from 'react';
import { 
  Wifi, 
  WifiOff, 
  Server, 
  ServerOff, 
  Clock, 
  AlertCircle,
  CheckCircle,
  RefreshCw
} from 'lucide-react';
import { useBackendStatus } from '../hooks/useElectron';

interface StatusBarProps {
  backendStatus: any;
  isOnline: boolean;
}

export const StatusBar: React.FC<StatusBarProps> = ({ 
  backendStatus, 
  isOnline 
}) => {
  const { restart } = useBackendStatus();
  const [currentTime, setCurrentTime] = React.useState(new Date());

  React.useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit',
      second: '2-digit'
    });
  };

  const getMarketStatus = () => {
    const now = new Date();
    const hour = now.getHours();
    const day = now.getDay();
    
    // Weekend
    if (day === 0 || day === 6) {
      return { status: 'Closed', color: 'text-gray-400' };
    }
    
    // Market hours (9:30 AM - 4:00 PM EST)
    if (hour >= 9.5 && hour < 16) {
      return { status: 'Open', color: 'text-green-400' };
    } else if (hour >= 4 && hour < 9.5) {
      return { status: 'Pre-Market', color: 'text-yellow-400' };
    } else if (hour >= 16 && hour < 20) {
      return { status: 'After Hours', color: 'text-blue-400' };
    } else {
      return { status: 'Closed', color: 'text-gray-400' };
    }
  };

  const marketStatus = getMarketStatus();

  return (
    <div className="flex items-center justify-between h-6 px-3 bg-gray-800 border-t border-gray-700 text-xs">
      {/* Left side - Backend and connection status */}
      <div className="flex items-center space-x-4">
        {/* Backend Status */}
        <div className="flex items-center space-x-1">
          {backendStatus.running ? (
            <>
              <Server size={12} className="text-green-400" />
              <span className="text-green-400">Backend Online</span>
              <span className="text-gray-400">:{backendStatus.port}</span>
            </>
          ) : (
            <>
              <ServerOff size={12} className="text-red-400" />
              <span className="text-red-400">Backend Offline</span>
              <button
                onClick={restart}
                className="ml-1 p-0.5 text-gray-400 hover:text-white transition-colors"
                title="Restart Backend"
              >
                <RefreshCw size={10} />
              </button>
            </>
          )}
        </div>

        {/* Network Status */}
        <div className="flex items-center space-x-1">
          {isOnline ? (
            <>
              <Wifi size={12} className="text-green-400" />
              <span className="text-green-400">Online</span>
            </>
          ) : (
            <>
              <WifiOff size={12} className="text-red-400" />
              <span className="text-red-400">Offline</span>
            </>
          )}
        </div>

        {/* Error indicator */}
        {backendStatus.error && (
          <div className="flex items-center space-x-1">
            <AlertCircle size={12} className="text-red-400" />
            <span className="text-red-400" title={backendStatus.error}>
              Error
            </span>
          </div>
        )}
      </div>

      {/* Right side - Market status and time */}
      <div className="flex items-center space-x-4">
        {/* Market Status */}
        <div className="flex items-center space-x-1">
          <div className={`w-2 h-2 rounded-full ${
            marketStatus.status === 'Open' ? 'bg-green-400' :
            marketStatus.status === 'Pre-Market' ? 'bg-yellow-400' :
            marketStatus.status === 'After Hours' ? 'bg-blue-400' :
            'bg-gray-400'
          }`} />
          <span className={marketStatus.color}>
            Market {marketStatus.status}
          </span>
        </div>

        {/* Current Time */}
        <div className="flex items-center space-x-1">
          <Clock size={12} className="text-gray-400" />
          <span className="text-gray-300">
            {formatTime(currentTime)}
          </span>
        </div>

        {/* App Status */}
        <div className="flex items-center space-x-1">
          <CheckCircle size={12} className="text-green-400" />
          <span className="text-green-400">Ready</span>
        </div>
      </div>
    </div>
  );
};

export default StatusBar;
