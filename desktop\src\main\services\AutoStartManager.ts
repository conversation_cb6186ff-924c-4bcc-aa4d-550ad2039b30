import { app } from 'electron';
import * as path from 'path';
import * as fs from 'fs';
import { ConfigManager } from '../config/ConfigManager';
import { Logger } from '../../shared/Logger';

export class AutoStartManager {
  private configManager: ConfigManager;
  private logger: Logger;

  constructor(configManager: ConfigManager) {
    this.configManager = configManager;
    this.logger = new Logger('AutoStartManager');
  }

  /**
   * Enable or disable auto-start functionality
   */
  async setAutoStart(enabled: boolean): Promise<void> {
    try {
      if (process.platform === 'darwin') {
        await this.setAutoStartMacOS(enabled);
      } else if (process.platform === 'win32') {
        await this.setAutoStartWindows(enabled);
      } else if (process.platform === 'linux') {
        await this.setAutoStartLinux(enabled);
      } else {
        throw new Error(`Auto-start not supported on platform: ${process.platform}`);
      }

      // Update configuration
      this.configManager.set('autoStart', enabled);
      
      this.logger.info(`Auto-start ${enabled ? 'enabled' : 'disabled'}`);
    } catch (error) {
      this.logger.error('Failed to set auto-start:', error);
      throw error;
    }
  }

  /**
   * Check if auto-start is currently enabled
   */
  async isAutoStartEnabled(): Promise<boolean> {
    try {
      if (process.platform === 'darwin') {
        return await this.isAutoStartEnabledMacOS();
      } else if (process.platform === 'win32') {
        return await this.isAutoStartEnabledWindows();
      } else if (process.platform === 'linux') {
        return await this.isAutoStartEnabledLinux();
      }
      return false;
    } catch (error) {
      this.logger.error('Failed to check auto-start status:', error);
      return false;
    }
  }

  /**
   * macOS auto-start implementation using Login Items
   */
  private async setAutoStartMacOS(enabled: boolean): Promise<void> {
    app.setLoginItemSettings({
      openAtLogin: enabled,
      openAsHidden: this.configManager.get('startMinimized', false),
      name: 'EntryAlert',
      path: process.execPath
    });
  }

  private async isAutoStartEnabledMacOS(): Promise<boolean> {
    const loginItemSettings = app.getLoginItemSettings();
    return loginItemSettings.openAtLogin;
  }

  /**
   * Windows auto-start implementation using Registry
   */
  private async setAutoStartWindows(enabled: boolean): Promise<void> {
    const { spawn } = require('child_process');
    const appName = 'EntryAlert';
    const execPath = process.execPath;

    return new Promise((resolve, reject) => {
      if (enabled) {
        // Add to registry
        const regCommand = `reg add "HKCU\\Software\\Microsoft\\Windows\\CurrentVersion\\Run" /v "${appName}" /t REG_SZ /d "${execPath}" /f`;
        
        const regProcess = spawn('cmd', ['/c', regCommand], { 
          windowsHide: true,
          stdio: 'pipe'
        });

        regProcess.on('close', (code) => {
          if (code === 0) {
            resolve();
          } else {
            reject(new Error(`Registry command failed with code ${code}`));
          }
        });

        regProcess.on('error', reject);
      } else {
        // Remove from registry
        const regCommand = `reg delete "HKCU\\Software\\Microsoft\\Windows\\CurrentVersion\\Run" /v "${appName}" /f`;
        
        const regProcess = spawn('cmd', ['/c', regCommand], { 
          windowsHide: true,
          stdio: 'pipe'
        });

        regProcess.on('close', (code) => {
          // Code 1 means the key doesn't exist, which is fine
          if (code === 0 || code === 1) {
            resolve();
          } else {
            reject(new Error(`Registry command failed with code ${code}`));
          }
        });

        regProcess.on('error', reject);
      }
    });
  }

  private async isAutoStartEnabledWindows(): Promise<boolean> {
    const { spawn } = require('child_process');
    const appName = 'EntryAlert';

    return new Promise((resolve) => {
      const regCommand = `reg query "HKCU\\Software\\Microsoft\\Windows\\CurrentVersion\\Run" /v "${appName}"`;
      
      const regProcess = spawn('cmd', ['/c', regCommand], { 
        windowsHide: true,
        stdio: 'pipe'
      });

      regProcess.on('close', (code) => {
        // Code 0 means the key exists
        resolve(code === 0);
      });

      regProcess.on('error', () => {
        resolve(false);
      });
    });
  }

  /**
   * Linux auto-start implementation using .desktop files
   */
  private async setAutoStartLinux(enabled: boolean): Promise<void> {
    const os = require('os');
    const autoStartDir = path.join(os.homedir(), '.config', 'autostart');
    const desktopFile = path.join(autoStartDir, 'entryalert.desktop');

    if (enabled) {
      // Create autostart directory if it doesn't exist
      if (!fs.existsSync(autoStartDir)) {
        fs.mkdirSync(autoStartDir, { recursive: true });
      }

      // Create .desktop file
      const desktopContent = `[Desktop Entry]
Type=Application
Name=EntryAlert
Comment=Stock Screener & Alert System
Exec=${process.execPath}
Icon=${path.join(__dirname, '../../assets/icon.png')}
Terminal=false
StartupNotify=true
Categories=Office;Finance;
X-GNOME-Autostart-enabled=true
`;

      fs.writeFileSync(desktopFile, desktopContent);
      
      // Make it executable
      fs.chmodSync(desktopFile, 0o755);
    } else {
      // Remove .desktop file
      if (fs.existsSync(desktopFile)) {
        fs.unlinkSync(desktopFile);
      }
    }
  }

  private async isAutoStartEnabledLinux(): Promise<boolean> {
    const os = require('os');
    const desktopFile = path.join(os.homedir(), '.config', 'autostart', 'entryalert.desktop');
    return fs.existsSync(desktopFile);
  }

  /**
   * Initialize auto-start based on configuration
   */
  async initialize(): Promise<void> {
    try {
      const shouldAutoStart = this.configManager.get('autoStart', false);
      const isCurrentlyEnabled = await this.isAutoStartEnabled();

      // Sync configuration with actual system state
      if (shouldAutoStart !== isCurrentlyEnabled) {
        await this.setAutoStart(shouldAutoStart);
      }

      this.logger.info(`Auto-start initialized: ${shouldAutoStart ? 'enabled' : 'disabled'}`);
    } catch (error) {
      this.logger.error('Failed to initialize auto-start:', error);
    }
  }

  /**
   * Toggle auto-start setting
   */
  async toggle(): Promise<boolean> {
    const currentState = await this.isAutoStartEnabled();
    const newState = !currentState;
    await this.setAutoStart(newState);
    return newState;
  }

  /**
   * Get auto-start status with additional information
   */
  async getStatus(): Promise<{
    enabled: boolean;
    supported: boolean;
    platform: string;
    configValue: boolean;
  }> {
    const supported = ['darwin', 'win32', 'linux'].includes(process.platform);
    const enabled = supported ? await this.isAutoStartEnabled() : false;
    const configValue = this.configManager.get('autoStart', false);

    return {
      enabled,
      supported,
      platform: process.platform,
      configValue
    };
  }

  /**
   * Repair auto-start if configuration and system state are out of sync
   */
  async repair(): Promise<void> {
    try {
      const status = await this.getStatus();
      
      if (status.supported && status.enabled !== status.configValue) {
        this.logger.warn('Auto-start configuration out of sync, repairing...');
        await this.setAutoStart(status.configValue);
        this.logger.info('Auto-start configuration repaired');
      }
    } catch (error) {
      this.logger.error('Failed to repair auto-start configuration:', error);
      throw error;
    }
  }
}
