import axios, { AxiosInstance } from 'axios';
import { ConfigManager } from '../config/ConfigManager';
import { Logger } from '../../shared/Logger';

export class DesktopApiService {
  private apiClient: AxiosInstance;
  private configManager: ConfigManager;
  private logger: Logger;
  private baseURL: string;

  constructor(configManager: ConfigManager) {
    this.configManager = configManager;
    this.logger = new Logger('DesktopApiService');
    this.baseURL = `http://localhost:${configManager.get('backendPort')}/api/v1`;
    
    this.apiClient = axios.create({
      baseURL: this.baseURL,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    this.setupInterceptors();
  }

  private setupInterceptors(): void {
    // Request interceptor
    this.apiClient.interceptors.request.use(
      (config) => {
        // Add auth token if available
        const token = this.configManager.get('authToken' as any);
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        
        this.logger.debug(`API Request: ${config.method?.toUpperCase()} ${config.url}`);
        return config;
      },
      (error) => {
        this.logger.error('API Request Error:', error);
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.apiClient.interceptors.response.use(
      (response) => {
        this.logger.debug(`API Response: ${response.status} ${response.config.url}`);
        return response;
      },
      (error) => {
        this.logger.error('API Response Error:', error.response?.data || error.message);
        
        // Handle specific error cases
        if (error.response?.status === 401) {
          // Token expired or invalid
          this.configManager.set('authToken' as any, '');
          // Emit event to notify renderer process
          this.emitToRenderer('auth:token-expired');
        }
        
        return Promise.reject(error);
      }
    );
  }

  // Authentication methods
  async login(credentials: { username: string; password: string }): Promise<any> {
    try {
      const formData = new FormData();
      formData.append('username', credentials.username);
      formData.append('password', credentials.password);

      const response = await this.apiClient.post('/auth/login', formData, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      });

      // Store auth token
      if (response.data.access_token) {
        this.configManager.set('authToken' as any, response.data.access_token);
      }

      return response.data;
    } catch (error) {
      this.logger.error('Login failed:', error);
      throw error;
    }
  }

  async logout(): Promise<void> {
    try {
      await this.apiClient.post('/auth/logout');
    } catch (error) {
      this.logger.error('Logout error:', error);
    } finally {
      // Clear stored token
      this.configManager.set('authToken' as any, '');
    }
  }

  async getCurrentUser(): Promise<any> {
    const response = await this.apiClient.get('/auth/me');
    return response.data;
  }

  // Stock methods
  async getStocks(params?: any): Promise<any> {
    const response = await this.apiClient.get('/stocks', { params });
    return response.data;
  }

  async getStock(symbol: string): Promise<any> {
    const response = await this.apiClient.get(`/stocks/${symbol}`);
    return response.data;
  }

  async searchStocks(query: string): Promise<any> {
    const response = await this.apiClient.get('/stocks/search', {
      params: { q: query }
    });
    return response.data;
  }

  async getStockHistory(symbol: string, period?: string): Promise<any> {
    const response = await this.apiClient.get(`/stocks/${symbol}/history`, {
      params: { period }
    });
    return response.data;
  }

  // Watchlist methods
  async getWatchlists(): Promise<any> {
    const response = await this.apiClient.get('/watchlists');
    return response.data;
  }

  async createWatchlist(data: any): Promise<any> {
    const response = await this.apiClient.post('/watchlists', data);
    return response.data;
  }

  async updateWatchlist(id: number, data: any): Promise<any> {
    const response = await this.apiClient.put(`/watchlists/${id}`, data);
    return response.data;
  }

  async deleteWatchlist(id: number): Promise<void> {
    await this.apiClient.delete(`/watchlists/${id}`);
  }

  async addStockToWatchlist(watchlistId: number, symbol: string): Promise<any> {
    const response = await this.apiClient.post(`/watchlists/${watchlistId}/stocks`, {
      symbol
    });
    return response.data;
  }

  async removeStockFromWatchlist(watchlistId: number, symbol: string): Promise<void> {
    await this.apiClient.delete(`/watchlists/${watchlistId}/stocks/${symbol}`);
  }

  // Alert methods
  async getAlerts(): Promise<any> {
    const response = await this.apiClient.get('/alerts');
    return response.data;
  }

  async createAlert(data: any): Promise<any> {
    const response = await this.apiClient.post('/alerts', data);
    return response.data;
  }

  async updateAlert(id: number, data: any): Promise<any> {
    const response = await this.apiClient.put(`/alerts/${id}`, data);
    return response.data;
  }

  async deleteAlert(id: number): Promise<void> {
    await this.apiClient.delete(`/alerts/${id}`);
  }

  async toggleAlert(id: number): Promise<any> {
    const response = await this.apiClient.patch(`/alerts/${id}/toggle`);
    return response.data;
  }

  // Screening methods
  async runScreener(criteria: any): Promise<any> {
    const response = await this.apiClient.post('/stocks/screen', criteria);
    return response.data;
  }

  async getScreenerResults(screenId: string): Promise<any> {
    const response = await this.apiClient.get(`/stocks/screen/${screenId}`);
    return response.data;
  }

  // Market data methods
  async getMarketStatus(): Promise<any> {
    const response = await this.apiClient.get('/stocks/market-status');
    return response.data;
  }

  async getMarketOverview(): Promise<any> {
    const response = await this.apiClient.get('/stocks/market-overview');
    return response.data;
  }

  // Health check
  async healthCheck(): Promise<boolean> {
    try {
      const response = await this.apiClient.get('/health');
      return response.status === 200;
    } catch (error) {
      return false;
    }
  }

  // Update base URL when backend port changes
  updateBaseURL(port: number): void {
    this.baseURL = `http://localhost:${port}/api/v1`;
    this.apiClient.defaults.baseURL = this.baseURL;
    this.logger.info(`API base URL updated to: ${this.baseURL}`);
  }

  // Get current base URL
  getBaseURL(): string {
    return this.baseURL;
  }

  // Emit events to renderer process
  private emitToRenderer(channel: string, data?: any): void {
    const { BrowserWindow } = require('electron');
    const mainWindow = BrowserWindow.getAllWindows()[0];
    if (mainWindow) {
      mainWindow.webContents.send(channel, data);
    }
  }

  // Generic API methods for custom requests
  async get<T>(endpoint: string, params?: any): Promise<T> {
    const response = await this.apiClient.get(endpoint, { params });
    return response.data;
  }

  async post<T>(endpoint: string, data?: any): Promise<T> {
    const response = await this.apiClient.post(endpoint, data);
    return response.data;
  }

  async put<T>(endpoint: string, data?: any): Promise<T> {
    const response = await this.apiClient.put(endpoint, data);
    return response.data;
  }

  async patch<T>(endpoint: string, data?: any): Promise<T> {
    const response = await this.apiClient.patch(endpoint, data);
    return response.data;
  }

  async delete<T>(endpoint: string): Promise<T> {
    const response = await this.apiClient.delete(endpoint);
    return response.data;
  }
}
