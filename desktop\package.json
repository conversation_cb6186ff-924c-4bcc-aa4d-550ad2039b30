{"name": "entryalert-desktop", "version": "1.0.0", "description": "EntryAlert Desktop - Cross-platform stock screener desktop application", "main": "dist/main.js", "homepage": "./", "author": "EntryAlert Team", "license": "MIT", "private": true, "scripts": {"dev": "node scripts/dev.js", "dev:all": "node scripts/dev.js all", "dev:backend": "node scripts/dev.js backend", "dev:frontend": "node scripts/dev.js frontend", "dev:electron": "node scripts/dev.js electron", "build": "node scripts/build.js build", "build:main": "tsc -p tsconfig.main.json", "build:frontend": "node scripts/build.js frontend", "start": "electron dist/main.js", "package": "node scripts/build.js package", "package:win": "node scripts/build.js package win", "package:mac": "node scripts/build.js package mac", "package:linux": "node scripts/build.js package linux", "package:win:x64": "node scripts/build.js package win x64", "package:win:ia32": "node scripts/build.js package win ia32", "package:mac:x64": "node scripts/build.js package mac x64", "package:mac:arm64": "node scripts/build.js package mac arm64", "package:linux:x64": "node scripts/build.js package linux x64", "dist": "npm run package", "dist:win": "npm run package:win", "dist:mac": "npm run package:mac", "dist:linux": "npm run package:linux", "clean": "node scripts/build.js clean", "postinstall": "electron-builder install-app-deps", "test": "jest", "lint": "eslint src --ext .ts --fix", "checksums": "node scripts/build.js checksums"}, "dependencies": {"electron-store": "^8.1.0", "electron-updater": "^6.1.7", "node-notifier": "^10.0.1", "axios": "^1.6.2", "ws": "^8.14.2"}, "devDependencies": {"@types/node": "^20.10.0", "@types/ws": "^8.5.10", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "concurrently": "^8.2.2", "electron": "^27.1.3", "electron-builder": "^24.6.4", "eslint": "^8.53.0", "jest": "^29.7.0", "rimraf": "^5.0.5", "typescript": "^5.2.2"}, "build": {"appId": "com.entryalert.desktop", "productName": "Entry<PERSON>lert", "directories": {"output": "build"}, "files": ["dist/**/*", "renderer/**/*", "assets/**/*", "node_modules/**/*"], "extraResources": [{"from": "../backend", "to": "backend", "filter": ["**/*", "!**/__pycache__", "!**/tests", "!**/test_*", "!**/*.pyc"]}], "mac": {"category": "public.app-category.finance", "icon": "assets/icon.icns", "target": [{"target": "dmg", "arch": ["x64", "arm64"]}]}, "win": {"icon": "assets/icon.ico", "target": [{"target": "nsis", "arch": ["x64", "ia32"]}]}, "linux": {"icon": "assets/icon.png", "category": "Office", "target": [{"target": "AppImage", "arch": ["x64"]}, {"target": "deb", "arch": ["x64"]}]}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true}}}