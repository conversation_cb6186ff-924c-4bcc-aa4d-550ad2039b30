"""
Tests for background tasks, alert processing, and notification systems.
"""
import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from datetime import datetime, timedelta
from sqlalchemy.orm import Session

from app.core.background_tasks import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, task_manager
from app.models.stock import Stock
from app.models.alert import <PERSON><PERSON>, AlertStatus, AlertType, AlertPriority
from app.models.user import User
from app.services.email_service import email_service
from app.services.push_service import push_service
from app.services.cache_service import cache_service
from app.database import SessionLocal


@pytest.fixture
def mock_db_session():
    """Mock database session."""
    session = Mock(spec=Session)
    session.query.return_value = session
    session.filter.return_value = session
    session.join.return_value = session
    session.limit.return_value = session
    session.all.return_value = []
    session.first.return_value = None
    session.commit.return_value = None
    session.rollback.return_value = None
    session.close.return_value = None
    return session


@pytest.fixture
def sample_stock():
    """Create a sample stock for testing."""
    return Stock(
        id=1,
        symbol="AAPL",
        name="Apple Inc.",
        current_price=150.0,
        previous_close=148.0,
        volume=1000000,
        avg_volume=800000,
        is_active=True,
        last_data_update=datetime.utcnow()
    )


@pytest.fixture
def sample_user():
    """Create a sample user for testing."""
    return User(
        id=1,
        email="<EMAIL>",
        username="testuser",
        first_name="Test",
        last_name="User",
        notification_email=True,
        notification_push=True,
        is_active=True
    )


@pytest.fixture
def sample_alert(sample_user, sample_stock):
    """Create a sample alert for testing."""
    return Alert(
        id=1,
        user_id=sample_user.id,
        stock_id=sample_stock.id,
        alert_type=AlertType.PRICE_ABOVE,
        title="AAPL Price Alert",
        message="AAPL has crossed above $150.00",
        conditions={"target_price": 149.0},
        priority=AlertPriority.HIGH,
        status=AlertStatus.ACTIVE,
        user=sample_user,
        stock=sample_stock
    )


class TestBackgroundTaskManager:
    """Test the BackgroundTaskManager class."""
    
    def test_init(self):
        """Test BackgroundTaskManager initialization."""
        manager = BackgroundTaskManager()
        assert manager.scheduler is not None
        assert manager.is_running is False
        assert manager.market_data_circuit_breaker is not None
    
    @pytest.mark.asyncio
    async def test_start_stop(self):
        """Test starting and stopping background tasks."""
        manager = BackgroundTaskManager()
        
        # Test start
        await manager.start()
        assert manager.is_running is True
        assert manager.scheduler.running is True
        
        # Test stop
        await manager.stop()
        assert manager.is_running is False
    
    @pytest.mark.asyncio
    async def test_start_already_running(self):
        """Test starting when already running."""
        manager = BackgroundTaskManager()
        
        await manager.start()
        assert manager.is_running is True
        
        # Starting again should not raise error
        await manager.start()
        assert manager.is_running is True
        
        await manager.stop()


class TestMarketDataUpdate:
    """Test market data update functionality."""
    
    @pytest.mark.asyncio
    @patch('app.core.background_tasks.SessionLocal')
    @patch('app.core.background_tasks.market_data_service')
    @patch('app.core.background_tasks.cache_service')
    async def test_update_market_data_success(self, mock_cache, mock_market_service, mock_session_local, sample_stock):
        """Test successful market data update."""
        # Setup mocks
        mock_db = Mock()
        mock_session_local.return_value = mock_db
        mock_db.query.return_value.filter.return_value.limit.return_value.all.return_value = [sample_stock]
        
        mock_market_service.get_multiple_quotes = AsyncMock(return_value={
            "AAPL": {
                "current_price": 151.0,
                "previous_close": 150.0,
                "volume": 1200000
            }
        })
        
        mock_cache.set_stock_data = AsyncMock(return_value=True)
        
        # Test
        manager = BackgroundTaskManager()
        await manager.update_market_data()
        
        # Verify
        mock_market_service.get_multiple_quotes.assert_called()
        mock_cache.set_stock_data.assert_called()
        mock_db.commit.assert_called_once()
    
    @pytest.mark.asyncio
    @patch('app.core.background_tasks.SessionLocal')
    async def test_update_market_data_no_stocks(self, mock_session_local):
        """Test market data update with no active stocks."""
        # Setup mocks
        mock_db = Mock()
        mock_session_local.return_value = mock_db
        mock_db.query.return_value.filter.return_value.limit.return_value.all.return_value = []
        
        # Test
        manager = BackgroundTaskManager()
        await manager.update_market_data()
        
        # Verify
        mock_db.commit.assert_not_called()
    
    @pytest.mark.asyncio
    @patch('app.core.background_tasks.SessionLocal')
    @patch('app.core.background_tasks.market_data_service')
    async def test_update_market_data_api_failure(self, mock_market_service, mock_session_local, sample_stock):
        """Test market data update with API failure."""
        # Setup mocks
        mock_db = Mock()
        mock_session_local.return_value = mock_db
        mock_db.query.return_value.filter.return_value.limit.return_value.all.return_value = [sample_stock]
        
        mock_market_service.get_multiple_quotes = AsyncMock(side_effect=Exception("API Error"))
        
        # Test
        manager = BackgroundTaskManager()
        await manager.update_market_data()
        
        # Verify - should handle error gracefully
        mock_db.rollback.assert_called_once()


class TestAlertProcessing:
    """Test alert processing functionality."""
    
    @pytest.mark.asyncio
    @patch('app.core.background_tasks.SessionLocal')
    @patch('app.core.background_tasks.email_service')
    @patch('app.core.background_tasks.push_service')
    @patch('app.core.background_tasks.websocket_manager')
    async def test_check_alerts_price_above(self, mock_websocket, mock_push, mock_email, mock_session_local, sample_alert):
        """Test price above alert triggering."""
        # Setup mocks
        mock_db = Mock()
        mock_session_local.return_value = mock_db
        mock_db.query.return_value.filter.return_value.join.return_value.all.return_value = [sample_alert]
        mock_db.query.return_value.filter.return_value.first.return_value = sample_alert.user
        
        mock_email.send_alert_notification = AsyncMock(return_value=True)
        mock_push.send_alert_notification = AsyncMock(return_value=True)
        mock_websocket.send_alert_notification = AsyncMock()
        
        # Test
        manager = BackgroundTaskManager()
        await manager.check_alerts()
        
        # Verify alert was triggered
        assert sample_alert.status == AlertStatus.TRIGGERED
        assert sample_alert.triggered_at is not None
        assert sample_alert.trigger_price == 150.0
        
        # Verify notifications were sent
        mock_email.send_alert_notification.assert_called_once()
        mock_push.send_alert_notification.assert_called_once()
        mock_websocket.send_alert_notification.assert_called_once()
    
    @pytest.mark.asyncio
    @patch('app.core.background_tasks.SessionLocal')
    async def test_check_alerts_no_trigger(self, mock_session_local, sample_alert):
        """Test alert that should not trigger."""
        # Modify alert to not trigger
        sample_alert.conditions = {"target_price": 155.0}  # Higher than current price
        
        # Setup mocks
        mock_db = Mock()
        mock_session_local.return_value = mock_db
        mock_db.query.return_value.filter.return_value.join.return_value.all.return_value = [sample_alert]
        
        # Test
        manager = BackgroundTaskManager()
        await manager.check_alerts()
        
        # Verify alert was not triggered
        assert sample_alert.status == AlertStatus.ACTIVE
        assert sample_alert.triggered_at is None
    
    @pytest.mark.asyncio
    @patch('app.core.background_tasks.SessionLocal')
    async def test_check_alerts_volume_surge(self, mock_session_local, sample_stock, sample_user):
        """Test volume surge alert."""
        # Create volume surge alert
        alert = Alert(
            id=2,
            user_id=sample_user.id,
            stock_id=sample_stock.id,
            alert_type=AlertType.VOLUME_SURGE,
            title="AAPL Volume Alert",
            message="AAPL volume surge detected",
            conditions={"threshold": 1.2},  # 20% above average
            status=AlertStatus.ACTIVE,
            user=sample_user,
            stock=sample_stock
        )
        
        # Setup mocks
        mock_db = Mock()
        mock_session_local.return_value = mock_db
        mock_db.query.return_value.filter.return_value.join.return_value.all.return_value = [alert]
        mock_db.query.return_value.filter.return_value.first.return_value = sample_user
        
        # Test
        manager = BackgroundTaskManager()
        await manager.check_alerts()
        
        # Verify alert was triggered (volume 1M > avg 800K * 1.2)
        assert alert.status == AlertStatus.TRIGGERED


class TestNotificationServices:
    """Test notification services."""
    
    @pytest.mark.asyncio
    async def test_email_service_configured(self):
        """Test email service configuration check."""
        with patch.object(email_service, '_is_configured', return_value=True):
            assert email_service._is_configured() is True
    
    @pytest.mark.asyncio
    async def test_push_service_configured(self):
        """Test push service configuration check."""
        with patch.object(push_service, '_is_configured', return_value=True):
            assert push_service._is_configured() is True
    
    @pytest.mark.asyncio
    @patch('app.services.email_service.aiosmtplib.send')
    async def test_email_notification_success(self, mock_send, sample_alert, sample_user):
        """Test successful email notification."""
        mock_send.return_value = None
        
        with patch.object(email_service, '_is_configured', return_value=True):
            result = await email_service.send_alert_notification(sample_alert, sample_user)
            assert result is True
            mock_send.assert_called_once()


class TestCacheIntegration:
    """Test cache integration with background tasks."""
    
    @pytest.mark.asyncio
    async def test_cache_stock_data(self):
        """Test caching stock data."""
        test_data = {"symbol": "AAPL", "price": 150.0}
        
        with patch.object(cache_service.cache_manager, 'set', return_value=True):
            result = await cache_service.set_stock_data("AAPL", test_data)
            assert result is True
    
    @pytest.mark.asyncio
    async def test_cache_historical_data(self):
        """Test caching historical data."""
        test_data = [{"date": "2024-01-01", "close": 150.0}]
        
        with patch.object(cache_service.cache_manager, 'set', return_value=True):
            result = await cache_service.set_historical_data("AAPL", test_data)
            assert result is True


class TestErrorHandling:
    """Test error handling and retry mechanisms."""
    
    @pytest.mark.asyncio
    async def test_retry_mechanism(self):
        """Test retry mechanism with temporary failures."""
        from app.utils.retry import retry_async, RetryConfig
        
        call_count = 0
        
        @retry_async(config=RetryConfig(max_attempts=3, base_delay=0.1))
        async def failing_function():
            nonlocal call_count
            call_count += 1
            if call_count < 3:
                raise Exception("Temporary failure")
            return "success"
        
        result = await failing_function()
        assert result == "success"
        assert call_count == 3
    
    @pytest.mark.asyncio
    async def test_circuit_breaker(self):
        """Test circuit breaker functionality."""
        from app.utils.retry import CircuitBreaker
        
        circuit_breaker = CircuitBreaker(failure_threshold=2, recovery_timeout=0.1)
        
        @circuit_breaker
        async def failing_function():
            raise Exception("Service unavailable")
        
        # First failure
        with pytest.raises(Exception):
            await failing_function()
        
        # Second failure - should open circuit
        with pytest.raises(Exception):
            await failing_function()
        
        # Third call - should be blocked by circuit breaker
        with pytest.raises(Exception, match="Circuit breaker is OPEN"):
            await failing_function()


if __name__ == "__main__":
    pytest.main([__file__])
