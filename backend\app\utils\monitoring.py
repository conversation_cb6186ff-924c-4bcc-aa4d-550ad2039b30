"""
Monitoring utilities and decorators.
"""
import time
import functools
from typing import Callable, Any
from fastapi import Request, Response

from ..services.monitoring_service import performance_monitor
from ..utils.logger import get_logger

logger = get_logger(__name__)


def monitor_background_task(task_name: str):
    """Decorator to monitor background task performance."""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            start_time = time.time()
            success = True
            
            try:
                result = await func(*args, **kwargs)
                return result
            except Exception as e:
                success = False
                logger.error(f"Background task {task_name} failed: {e}")
                raise
            finally:
                duration = time.time() - start_time
                performance_monitor.record_background_task(task_name, duration, success)
        
        return wrapper
    return decorator


def monitor_database_query(query_type: str, table: str = None):
    """Decorator to monitor database query performance."""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            start_time = time.time()
            
            try:
                result = await func(*args, **kwargs)
                return result
            finally:
                duration = time.time() - start_time
                performance_monitor.record_database_query(query_type, duration, table)
        
        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs):
            start_time = time.time()
            
            try:
                result = func(*args, **kwargs)
                return result
            finally:
                duration = time.time() - start_time
                performance_monitor.record_database_query(query_type, duration, table)
        
        # Return appropriate wrapper based on function type
        if hasattr(func, '__code__') and func.__code__.co_flags & 0x80:  # CO_COROUTINE
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator


def monitor_cache_operation(operation: str):
    """Decorator to monitor cache operation performance."""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            start_time = time.time()
            hit = False
            
            try:
                result = await func(*args, **kwargs)
                # Determine if it was a cache hit based on result
                hit = result is not None and operation == 'get'
                return result
            finally:
                duration = time.time() - start_time
                performance_monitor.record_cache_operation(operation, hit, duration)
        
        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs):
            start_time = time.time()
            hit = False
            
            try:
                result = func(*args, **kwargs)
                # Determine if it was a cache hit based on result
                hit = result is not None and operation == 'get'
                return result
            finally:
                duration = time.time() - start_time
                performance_monitor.record_cache_operation(operation, hit, duration)
        
        # Return appropriate wrapper based on function type
        if hasattr(func, '__code__') and func.__code__.co_flags & 0x80:  # CO_COROUTINE
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator


def monitor_market_data_fetch(provider: str):
    """Decorator to monitor market data fetch performance."""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            start_time = time.time()
            success = True
            symbol = "unknown"
            
            # Try to extract symbol from arguments
            try:
                if args and isinstance(args[0], str):
                    symbol = args[0]
                elif 'symbol' in kwargs:
                    symbol = kwargs['symbol']
            except:
                pass
            
            try:
                result = await func(*args, **kwargs)
                return result
            except Exception as e:
                success = False
                logger.error(f"Market data fetch failed for {symbol} from {provider}: {e}")
                raise
            finally:
                duration = time.time() - start_time
                performance_monitor.record_market_data_fetch(provider, symbol, duration, success)
        
        return wrapper
    return decorator


class APIMonitoringMiddleware:
    """Middleware to monitor API request performance."""
    
    def __init__(self, app):
        self.app = app
    
    async def __call__(self, scope, receive, send):
        if scope["type"] != "http":
            await self.app(scope, receive, send)
            return
        
        start_time = time.time()
        
        # Create a custom send function to capture response status
        status_code = 500  # Default to error
        
        async def custom_send(message):
            nonlocal status_code
            if message["type"] == "http.response.start":
                status_code = message["status"]
            await send(message)
        
        try:
            await self.app(scope, receive, custom_send)
        finally:
            # Record the API request metrics
            duration = time.time() - start_time
            endpoint = scope.get("path", "unknown")
            method = scope.get("method", "unknown")
            
            performance_monitor.record_api_request(endpoint, method, duration, status_code)


# Context manager for monitoring code blocks
class MonitoringContext:
    """Context manager for monitoring arbitrary code blocks."""
    
    def __init__(self, metric_name: str, tags: dict = None):
        self.metric_name = metric_name
        self.tags = tags or {}
        self.start_time = None
    
    async def __aenter__(self):
        self.start_time = time.time()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.start_time:
            duration = time.time() - self.start_time
            success = exc_type is None
            self.tags['success'] = str(success)
            performance_monitor.metrics_collector.record_metric(
                self.metric_name, duration, self.tags
            )
    
    def __enter__(self):
        self.start_time = time.time()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.start_time:
            duration = time.time() - self.start_time
            success = exc_type is None
            self.tags['success'] = str(success)
            performance_monitor.metrics_collector.record_metric(
                self.metric_name, duration, self.tags
            )


# Utility functions
def get_performance_summary(duration_minutes: int = 60) -> dict:
    """Get performance summary for the specified duration."""
    return performance_monitor.get_performance_summary(duration_minutes)


def get_api_stats(duration_minutes: int = 60) -> dict:
    """Get API endpoint statistics."""
    return performance_monitor.get_api_endpoint_stats(duration_minutes)


def get_background_task_stats(duration_minutes: int = 60) -> dict:
    """Get background task statistics."""
    return performance_monitor.get_background_task_stats(duration_minutes)


def get_system_metrics() -> dict:
    """Get current system metrics."""
    return performance_monitor.get_system_metrics()
