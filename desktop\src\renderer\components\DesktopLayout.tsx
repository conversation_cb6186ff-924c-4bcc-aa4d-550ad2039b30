import React, { useEffect, useState } from 'react';
import { TitleBar } from './TitleBar';
import { StatusBar } from './StatusBar';
import { useElectron, useBackendStatus, useElectronNotifications } from '../hooks/useElectron';

interface DesktopLayoutProps {
  children: React.ReactNode;
}

export const DesktopLayout: React.FC<DesktopLayoutProps> = ({ children }) => {
  const { isElectron, electronAPI } = useElectron();
  const { status: backendStatus } = useBackendStatus();
  const { showSystemNotification } = useElectronNotifications();
  const [isOnline, setIsOnline] = useState(navigator.onLine);

  useEffect(() => {
    // Handle online/offline status
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  useEffect(() => {
    if (!electronAPI) return;

    // Handle navigation events from main process
    const handleNavigate = (route: string) => {
      // Use your router to navigate
      window.location.hash = route;
    };

    // Handle notification toggles
    const handleToggleNotifications = (enabled: boolean) => {
      // Update notification settings
      console.log('Notifications toggled:', enabled);
    };

    const handleToggleSoundAlerts = (enabled: boolean) => {
      // Update sound alert settings
      console.log('Sound alerts toggled:', enabled);
    };

    const handleShowAbout = () => {
      // Show about dialog
      console.log('Show about dialog');
    };

    const handleNavigateToStock = (symbol: string) => {
      // Navigate to stock detail page
      window.location.hash = `/stock/${symbol}`;
    };

    const handleNotificationAction = (data: any) => {
      // Handle notification actions
      console.log('Notification action:', data);
    };

    // Set up event listeners
    electronAPI.on('navigate', handleNavigate);
    electronAPI.on('toggle-notifications', handleToggleNotifications);
    electronAPI.on('toggle-sound-alerts', handleToggleSoundAlerts);
    electronAPI.on('show-about', handleShowAbout);
    electronAPI.on('navigate-to-stock', handleNavigateToStock);
    electronAPI.on('notification-action', handleNotificationAction);

    // Cleanup
    return () => {
      electronAPI.off('navigate', handleNavigate);
      electronAPI.off('toggle-notifications', handleToggleNotifications);
      electronAPI.off('toggle-sound-alerts', handleToggleSoundAlerts);
      electronAPI.off('show-about', handleShowAbout);
      electronAPI.off('navigate-to-stock', handleNavigateToStock);
      electronAPI.off('notification-action', handleNotificationAction);
    };
  }, [electronAPI]);

  // Show system notification when backend status changes
  useEffect(() => {
    if (backendStatus.error) {
      showSystemNotification(
        'Backend Error',
        backendStatus.error,
        'error'
      );
    }
  }, [backendStatus.error, showSystemNotification]);

  if (!isElectron) {
    // Return regular web layout
    return <>{children}</>;
  }

  return (
    <div className="flex flex-col h-screen bg-gray-900">
      <TitleBar />
      
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Main content area */}
        <div className="flex-1 overflow-hidden">
          {children}
        </div>
        
        {/* Status bar */}
        <StatusBar 
          backendStatus={backendStatus}
          isOnline={isOnline}
        />
      </div>
    </div>
  );
};

export default DesktopLayout;
