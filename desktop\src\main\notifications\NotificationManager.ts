import { Notification, nativeImage } from 'electron';
import * as path from 'path';
import { Logger } from '../../shared/Logger';

export interface NotificationOptions {
  title: string;
  body: string;
  icon?: string;
  silent?: boolean;
  urgency?: 'normal' | 'critical' | 'low';
  actions?: Array<{
    type: string;
    text: string;
  }>;
  tag?: string;
  data?: any;
}

export class NotificationManager {
  private logger: Logger;
  private notificationQueue: NotificationOptions[] = [];
  private isProcessingQueue = false;
  private readonly MAX_CONCURRENT_NOTIFICATIONS = 3;
  private activeNotifications: Set<Notification> = new Set();

  constructor() {
    this.logger = new Logger('NotificationManager');
  }

  show(options: NotificationOptions): void {
    if (!Notification.isSupported()) {
      this.logger.warn('Notifications are not supported on this system');
      return;
    }

    // Add to queue if we have too many active notifications
    if (this.activeNotifications.size >= this.MAX_CONCURRENT_NOTIFICATIONS) {
      this.notificationQueue.push(options);
      return;
    }

    this.createNotification(options);
  }

  showStockAlert(symbol: string, price: number, change: number, alertType: string): void {
    const changeText = change >= 0 ? `+${change.toFixed(2)}%` : `${change.toFixed(2)}%`;
    const changeColor = change >= 0 ? '🟢' : '🔴';
    
    this.show({
      title: `${changeColor} ${symbol} Alert`,
      body: `${alertType}\nPrice: $${price.toFixed(2)} (${changeText})`,
      icon: this.getStockAlertIcon(alertType),
      urgency: 'normal',
      tag: `stock-alert-${symbol}`,
      data: {
        type: 'stock-alert',
        symbol,
        price,
        change,
        alertType
      }
    });
  }

  showMarketStatusNotification(status: string): void {
    const statusEmoji = this.getMarketStatusEmoji(status);
    
    this.show({
      title: `${statusEmoji} Market ${status}`,
      body: `The market is now ${status.toLowerCase()}`,
      icon: this.getMarketStatusIcon(),
      silent: true,
      urgency: 'low',
      tag: 'market-status'
    });
  }

  showSystemNotification(title: string, message: string, type: 'info' | 'warning' | 'error' = 'info'): void {
    this.show({
      title,
      body: message,
      icon: this.getSystemIcon(type),
      urgency: type === 'error' ? 'critical' : 'normal',
      tag: `system-${type}`
    });
  }

  private createNotification(options: NotificationOptions): void {
    try {
      const notification = new Notification({
        title: options.title,
        body: options.body,
        icon: options.icon || this.getDefaultIcon(),
        silent: options.silent || false,
        urgency: options.urgency || 'normal',
        actions: options.actions || [],
        tag: options.tag
      });

      // Track active notification
      this.activeNotifications.add(notification);

      // Handle notification events
      notification.on('show', () => {
        this.logger.debug(`Notification shown: ${options.title}`);
      });

      notification.on('click', () => {
        this.logger.debug(`Notification clicked: ${options.title}`);
        this.handleNotificationClick(options);
      });

      notification.on('close', () => {
        this.logger.debug(`Notification closed: ${options.title}`);
        this.activeNotifications.delete(notification);
        this.processQueue();
      });

      notification.on('action', (event, index) => {
        this.logger.debug(`Notification action ${index} clicked: ${options.title}`);
        this.handleNotificationAction(options, index);
      });

      notification.on('failed', (event, error) => {
        this.logger.error(`Notification failed: ${options.title}`, error);
        this.activeNotifications.delete(notification);
        this.processQueue();
      });

      // Show the notification
      notification.show();

      // Auto-close after 10 seconds for non-critical notifications
      if (options.urgency !== 'critical') {
        setTimeout(() => {
          if (this.activeNotifications.has(notification)) {
            notification.close();
          }
        }, 10000);
      }

    } catch (error) {
      this.logger.error('Failed to create notification:', error);
    }
  }

  private processQueue(): void {
    if (this.isProcessingQueue || this.notificationQueue.length === 0) {
      return;
    }

    if (this.activeNotifications.size < this.MAX_CONCURRENT_NOTIFICATIONS) {
      this.isProcessingQueue = true;
      const nextNotification = this.notificationQueue.shift();
      
      if (nextNotification) {
        this.createNotification(nextNotification);
      }
      
      this.isProcessingQueue = false;
      
      // Process more if queue is not empty
      if (this.notificationQueue.length > 0) {
        setTimeout(() => this.processQueue(), 100);
      }
    }
  }

  private handleNotificationClick(options: NotificationOptions): void {
    // Handle different types of notification clicks
    if (options.data?.type === 'stock-alert') {
      // Navigate to stock detail page
      this.sendToRenderer('navigate-to-stock', options.data.symbol);
    }
  }

  private handleNotificationAction(options: NotificationOptions, actionIndex: number): void {
    const action = options.actions?.[actionIndex];
    if (action) {
      this.sendToRenderer('notification-action', {
        type: action.type,
        data: options.data
      });
    }
  }

  private sendToRenderer(channel: string, data: any): void {
    const { BrowserWindow } = require('electron');
    const mainWindow = BrowserWindow.getAllWindows()[0];
    if (mainWindow) {
      mainWindow.webContents.send(channel, data);
    }
  }

  private getDefaultIcon(): string {
    return path.join(__dirname, '../../assets/icon.png');
  }

  private getStockAlertIcon(alertType: string): string {
    const iconMap: { [key: string]: string } = {
      'price-target': 'price-alert.png',
      'volume-spike': 'volume-alert.png',
      'breakout': 'breakout-alert.png',
      'support-resistance': 'support-resistance-alert.png'
    };

    const iconName = iconMap[alertType] || 'stock-alert.png';
    return path.join(__dirname, '../../assets/notifications', iconName);
  }

  private getMarketStatusIcon(): string {
    return path.join(__dirname, '../../assets/notifications/market-status.png');
  }

  private getSystemIcon(type: 'info' | 'warning' | 'error'): string {
    const iconMap = {
      info: 'info.png',
      warning: 'warning.png',
      error: 'error.png'
    };

    return path.join(__dirname, '../../assets/notifications', iconMap[type]);
  }

  private getMarketStatusEmoji(status: string): string {
    const emojiMap: { [key: string]: string } = {
      'Open': '🔔',
      'Pre-Market': '🌅',
      'After Hours': '🌙',
      'Closed': '🔕'
    };

    return emojiMap[status] || '📊';
  }

  clearAll(): void {
    this.activeNotifications.forEach(notification => {
      notification.close();
    });
    this.activeNotifications.clear();
    this.notificationQueue = [];
  }

  getActiveCount(): number {
    return this.activeNotifications.size;
  }

  getQueuedCount(): number {
    return this.notificationQueue.length;
  }
}
