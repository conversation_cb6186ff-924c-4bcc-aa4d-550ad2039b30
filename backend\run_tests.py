#!/usr/bin/env python3
"""
Test runner script for EntryAlert backend tests.
"""
import sys
import os
import subprocess
import argparse

# Add the backend directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def run_tests(test_path=None, verbose=False, coverage=False):
    """Run tests with optional coverage."""
    cmd = ["python", "-m", "pytest"]
    
    if verbose:
        cmd.append("-v")
    
    if coverage:
        cmd.extend(["--cov=app", "--cov-report=html", "--cov-report=term"])
    
    if test_path:
        cmd.append(test_path)
    
    print(f"Running command: {' '.join(cmd)}")
    result = subprocess.run(cmd, cwd=os.path.dirname(os.path.abspath(__file__)))
    return result.returncode

def main():
    parser = argparse.ArgumentParser(description="Run EntryAlert backend tests")
    parser.add_argument("test_path", nargs="?", help="Specific test file or directory to run")
    parser.add_argument("-v", "--verbose", action="store_true", help="Verbose output")
    parser.add_argument("-c", "--coverage", action="store_true", help="Run with coverage")
    parser.add_argument("--background-tasks", action="store_true", help="Run only background task tests")
    
    args = parser.parse_args()
    
    if args.background_tasks:
        test_path = "tests/test_background_tasks.py"
    else:
        test_path = args.test_path
    
    return run_tests(test_path, args.verbose, args.coverage)

if __name__ == "__main__":
    sys.exit(main())
