#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

const PLATFORMS = {
  win: 'win32',
  mac: 'darwin',
  linux: 'linux'
};

const ARCHITECTURES = {
  x64: 'x64',
  arm64: 'arm64',
  ia32: 'ia32'
};

class BuildManager {
  constructor() {
    this.rootDir = path.join(__dirname, '..');
    this.frontendDir = path.join(this.rootDir, '..', 'frontend');
    this.backendDir = path.join(this.rootDir, '..', 'backend');
  }

  log(message) {
    console.log(`[BUILD] ${message}`);
  }

  error(message) {
    console.error(`[ERROR] ${message}`);
  }

  exec(command, cwd = this.rootDir) {
    this.log(`Executing: ${command}`);
    try {
      execSync(command, { 
        cwd, 
        stdio: 'inherit',
        env: { ...process.env, NODE_ENV: 'production' }
      });
    } catch (error) {
      this.error(`Command failed: ${command}`);
      throw error;
    }
  }

  async clean() {
    this.log('Cleaning build directories...');
    
    const dirsToClean = [
      path.join(this.rootDir, 'dist'),
      path.join(this.rootDir, 'build'),
      path.join(this.rootDir, 'renderer'),
      path.join(this.frontendDir, 'dist')
    ];

    dirsToClean.forEach(dir => {
      if (fs.existsSync(dir)) {
        fs.rmSync(dir, { recursive: true, force: true });
        this.log(`Cleaned: ${dir}`);
      }
    });
  }

  async installDependencies() {
    this.log('Installing desktop dependencies...');
    this.exec('npm install');

    this.log('Installing frontend dependencies...');
    this.exec('npm install', this.frontendDir);
  }

  async buildFrontend() {
    this.log('Building React frontend...');
    this.exec('npm run build', this.frontendDir);

    // Copy frontend build to desktop renderer directory
    const frontendDist = path.join(this.frontendDir, 'dist');
    const rendererDir = path.join(this.rootDir, 'renderer');
    
    if (fs.existsSync(frontendDist)) {
      fs.cpSync(frontendDist, rendererDir, { recursive: true });
      this.log('Frontend build copied to renderer directory');
    } else {
      throw new Error('Frontend build not found');
    }
  }

  async buildMain() {
    this.log('Building Electron main process...');
    this.exec('npm run build:main');
  }

  async packageApp(platform, arch) {
    this.log(`Packaging for ${platform}-${arch}...`);
    
    const platformFlag = platform === 'all' ? '' : `--${platform}`;
    const archFlag = arch ? ` --${arch}` : '';
    
    this.exec(`npm run dist${platformFlag}${archFlag}`);
  }

  async createInstallers() {
    this.log('Creating installers...');
    
    const buildDir = path.join(this.rootDir, 'build');
    if (!fs.existsSync(buildDir)) {
      this.error('Build directory not found. Run packaging first.');
      return;
    }

    // The electron-builder configuration in package.json handles installer creation
    this.log('Installers created by electron-builder');
  }

  async copyBackend() {
    this.log('Preparing backend for packaging...');
    
    const backendDest = path.join(this.rootDir, 'backend-dist');
    
    // Clean existing backend dist
    if (fs.existsSync(backendDest)) {
      fs.rmSync(backendDest, { recursive: true, force: true });
    }

    // Copy backend files (excluding unnecessary files)
    const excludePatterns = [
      '__pycache__',
      '*.pyc',
      'tests',
      'test_*',
      '.pytest_cache',
      'venv',
      '.env',
      '*.db',
      'logs'
    ];

    this.copyDirectory(this.backendDir, backendDest, excludePatterns);
    this.log('Backend prepared for packaging');
  }

  copyDirectory(src, dest, excludePatterns = []) {
    if (!fs.existsSync(dest)) {
      fs.mkdirSync(dest, { recursive: true });
    }

    const items = fs.readdirSync(src);
    
    items.forEach(item => {
      const srcPath = path.join(src, item);
      const destPath = path.join(dest, item);
      
      // Check if item should be excluded
      const shouldExclude = excludePatterns.some(pattern => {
        if (pattern.includes('*')) {
          const regex = new RegExp(pattern.replace(/\*/g, '.*'));
          return regex.test(item);
        }
        return item === pattern;
      });

      if (shouldExclude) {
        return;
      }

      const stat = fs.statSync(srcPath);
      
      if (stat.isDirectory()) {
        this.copyDirectory(srcPath, destPath, excludePatterns);
      } else {
        fs.copyFileSync(srcPath, destPath);
      }
    });
  }

  async generateChecksums() {
    this.log('Generating checksums...');
    
    const crypto = require('crypto');
    const buildDir = path.join(this.rootDir, 'build');
    
    if (!fs.existsSync(buildDir)) {
      this.error('Build directory not found');
      return;
    }

    const files = fs.readdirSync(buildDir).filter(file => 
      file.endsWith('.exe') || 
      file.endsWith('.dmg') || 
      file.endsWith('.AppImage') || 
      file.endsWith('.deb')
    );

    const checksums = {};
    
    files.forEach(file => {
      const filePath = path.join(buildDir, file);
      const fileBuffer = fs.readFileSync(filePath);
      const hashSum = crypto.createHash('sha256');
      hashSum.update(fileBuffer);
      checksums[file] = hashSum.digest('hex');
    });

    const checksumsFile = path.join(buildDir, 'checksums.json');
    fs.writeFileSync(checksumsFile, JSON.stringify(checksums, null, 2));
    
    this.log(`Checksums generated: ${checksumsFile}`);
  }

  async buildAll() {
    try {
      await this.clean();
      await this.installDependencies();
      await this.buildFrontend();
      await this.buildMain();
      await this.copyBackend();
      
      this.log('Build completed successfully!');
    } catch (error) {
      this.error('Build failed');
      throw error;
    }
  }

  async packageAll() {
    try {
      await this.buildAll();
      await this.packageApp('all');
      await this.generateChecksums();
      
      this.log('Packaging completed successfully!');
    } catch (error) {
      this.error('Packaging failed');
      throw error;
    }
  }

  async packagePlatform(platform, arch) {
    try {
      await this.buildAll();
      await this.packageApp(platform, arch);
      await this.generateChecksums();
      
      this.log(`Packaging for ${platform}-${arch} completed successfully!`);
    } catch (error) {
      this.error(`Packaging for ${platform}-${arch} failed`);
      throw error;
    }
  }
}

// CLI interface
async function main() {
  const args = process.argv.slice(2);
  const command = args[0];
  const platform = args[1];
  const arch = args[2];

  const builder = new BuildManager();

  try {
    switch (command) {
      case 'clean':
        await builder.clean();
        break;
      
      case 'build':
        await builder.buildAll();
        break;
      
      case 'package':
        if (platform && platform !== 'all') {
          await builder.packagePlatform(platform, arch);
        } else {
          await builder.packageAll();
        }
        break;
      
      case 'frontend':
        await builder.buildFrontend();
        break;
      
      case 'main':
        await builder.buildMain();
        break;
      
      case 'checksums':
        await builder.generateChecksums();
        break;
      
      default:
        console.log(`
Usage: node build.js <command> [platform] [arch]

Commands:
  clean      - Clean build directories
  build      - Build all components
  package    - Build and package for all platforms
  frontend   - Build only frontend
  main       - Build only main process
  checksums  - Generate checksums for built packages

Platforms: win, mac, linux, all
Architectures: x64, arm64, ia32

Examples:
  node build.js build
  node build.js package
  node build.js package win x64
  node build.js package mac arm64
        `);
        process.exit(1);
    }
  } catch (error) {
    console.error('Build script failed:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = BuildManager;
