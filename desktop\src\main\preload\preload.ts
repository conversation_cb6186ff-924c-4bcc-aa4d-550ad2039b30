import { contextBridge, ipc<PERSON><PERSON><PERSON> } from 'electron';

// Define the API that will be exposed to the renderer process
const electronAPI = {
  // Window controls
  window: {
    minimize: () => ipcRenderer.invoke('window:minimize'),
    maximize: () => ipcRenderer.invoke('window:maximize'),
    close: () => ipcRenderer.invoke('window:close'),
    isMaximized: () => ipcRenderer.invoke('window:isMaximized')
  },

  // App controls
  app: {
    quit: () => ipcRenderer.invoke('app:quit'),
    getVersion: () => ipcRenderer.invoke('app:version'),
    getPlatform: () => process.platform,
    isPackaged: () => process.env.NODE_ENV === 'production'
  },

  // Configuration
  config: {
    get: (key: string) => ipcRenderer.invoke('config:get', key),
    set: (key: string, value: any) => ipcRenderer.invoke('config:set', key, value),
    getAll: () => ipcRenderer.invoke('config:getAll'),
    setAll: (config: any) => ipcRenderer.invoke('config:setAll', config),
    reset: () => ipcRenderer.invoke('config:reset')
  },

  // Notifications
  notifications: {
    show: (options: any) => ipcRenderer.invoke('notification:show', options),
    showStockAlert: (symbol: string, price: number, change: number, alertType: string) =>
      ipcRenderer.invoke('notification:showStockAlert', symbol, price, change, alertType),
    showMarketStatus: (status: string) =>
      ipcRenderer.invoke('notification:showMarketStatus', status),
    showSystem: (title: string, message: string, type?: string) =>
      ipcRenderer.invoke('notification:showSystem', title, message, type)
  },

  // Backend
  backend: {
    getStatus: () => ipcRenderer.invoke('backend:status'),
    restart: () => ipcRenderer.invoke('backend:restart'),
    start: () => ipcRenderer.invoke('backend:start'),
    stop: () => ipcRenderer.invoke('backend:stop')
  },

  // System
  system: {
    openExternal: (url: string) => ipcRenderer.invoke('shell:openExternal', url),
    showItemInFolder: (path: string) => ipcRenderer.invoke('shell:showItemInFolder', path),
    openPath: (path: string) => ipcRenderer.invoke('shell:openPath', path)
  },

  // File system
  fs: {
    selectFile: (options?: any) => ipcRenderer.invoke('dialog:selectFile', options),
    selectDirectory: (options?: any) => ipcRenderer.invoke('dialog:selectDirectory', options),
    saveFile: (options?: any) => ipcRenderer.invoke('dialog:saveFile', options),
    showMessageBox: (options: any) => ipcRenderer.invoke('dialog:showMessageBox', options)
  },

  // Auto-updater
  updater: {
    checkForUpdates: () => ipcRenderer.invoke('updater:checkForUpdates'),
    downloadUpdate: () => ipcRenderer.invoke('updater:downloadUpdate'),
    installUpdate: () => ipcRenderer.invoke('updater:installUpdate'),
    onUpdateAvailable: (callback: (info: any) => void) => {
      ipcRenderer.on('updater:update-available', (_, info) => callback(info));
    },
    onUpdateDownloaded: (callback: (info: any) => void) => {
      ipcRenderer.on('updater:update-downloaded', (_, info) => callback(info));
    },
    onUpdateError: (callback: (error: any) => void) => {
      ipcRenderer.on('updater:error', (_, error) => callback(error));
    }
  },

  // Event listeners
  on: (channel: string, callback: (...args: any[]) => void) => {
    const validChannels = [
      'navigate',
      'toggle-notifications',
      'toggle-sound-alerts',
      'show-about',
      'navigate-to-stock',
      'notification-action',
      'market-status-changed',
      'backend-status-changed',
      'config-changed'
    ];

    if (validChannels.includes(channel)) {
      ipcRenderer.on(channel, callback);
    }
  },

  off: (channel: string, callback: (...args: any[]) => void) => {
    ipcRenderer.removeListener(channel, callback);
  },

  once: (channel: string, callback: (...args: any[]) => void) => {
    const validChannels = [
      'navigate',
      'toggle-notifications',
      'toggle-sound-alerts',
      'show-about',
      'navigate-to-stock',
      'notification-action',
      'market-status-changed',
      'backend-status-changed',
      'config-changed'
    ];

    if (validChannels.includes(channel)) {
      ipcRenderer.once(channel, callback);
    }
  },

  // Store management
  store: {
    get: (key: string) => ipcRenderer.invoke('store:get', key),
    set: (key: string, value: any) => ipcRenderer.invoke('store:set', key, value),
    delete: (key: string) => ipcRenderer.invoke('store:delete', key),
    clear: () => ipcRenderer.invoke('store:clear'),
    has: (key: string) => ipcRenderer.invoke('store:has', key)
  },

  // Development utilities
  dev: {
    openDevTools: () => ipcRenderer.invoke('dev:openDevTools'),
    reload: () => ipcRenderer.invoke('dev:reload'),
    toggleDevTools: () => ipcRenderer.invoke('dev:toggleDevTools')
  }
};

// Expose the API to the renderer process
contextBridge.exposeInMainWorld('electronAPI', electronAPI);

// Type definitions for TypeScript
declare global {
  interface Window {
    electronAPI: typeof electronAPI;
  }
}

// Log that preload script has loaded
console.log('EntryAlert preload script loaded');

// Handle uncaught errors in preload
process.on('uncaughtException', (error) => {
  console.error('Uncaught exception in preload:', error);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled rejection in preload:', reason, promise);
});
