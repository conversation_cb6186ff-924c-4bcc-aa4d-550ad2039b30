# EntryAlert - US Stock Screener with Watchlist Notifications

## 🚀 Overview

EntryAlert is a comprehensive US stock screener application that processes 8000+ stocks in real-time, providing intelligent watchlist notifications and entry point alerts. Available as both a **web application** and a **cross-platform desktop application**, EntryAlert combines modern technologies with custom algorithms for optimal performance.

### 📱 Available Platforms

- **Web Application**: Access via browser at any time
- **Desktop Application**: Native apps for Windows, macOS, and Linux with system tray integration and offline capabilities

## 🏗️ System Architecture

```mermaid
graph TB
    subgraph "Frontend Layer"
        A[React/TypeScript App]
        B[WebSocket Client]
        C[Chart Components]
        D[Notification Center]
    end
    
    subgraph "Backend Layer"
        E[FastAPI Server]
        F[WebSocket Handler]
        G[Authentication Service]
        H[Background Jobs]
    end
    
    subgraph "Business Logic"
        I[Stock Screener Engine]
        J[Technical Indicators]
        K[Alert Generator]
        L[Watchlist Manager]
    end
    
    subgraph "Data Layer"
        M[PostgreSQL Database]
        N[Redis Cache]
        O[Market Data Store]
    end
    
    subgraph "External APIs"
        P[Alpha Vantage API]
        Q[Yahoo Finance API]
        R[IEX Cloud API]
    end
    
    A --> E
    B --> F
    E --> G
    E --> I
    F --> K
    H --> I
    I --> J
    I --> L
    E --> M
    E --> N
    I --> O
    H --> P
    H --> Q
    H --> R
    
    style A fill:#e1f5fe
    style E fill:#f3e5f5
    style I fill:#e8f5e8
    style M fill:#fff3e0
```

## 🔄 Application Workflow

```mermaid
sequenceDiagram
    participant U as User
    participant F as Frontend
    participant A as API Server
    participant S as Screener Engine
    participant D as Database
    participant E as External APIs
    participant N as Notification Service
    
    U->>F: Login/Register
    F->>A: Authentication Request
    A->>D: Validate Credentials
    D-->>A: User Data
    A-->>F: JWT Token
    
    U->>F: Create Watchlist
    F->>A: POST /watchlists
    A->>D: Store Watchlist
    D-->>A: Confirmation
    A-->>F: Success Response
    
    Note over S,E: Background Process
    S->>E: Fetch Market Data
    E-->>S: Stock Prices & Volume
    S->>S: Run Screening Algorithms
    S->>D: Update Stock Data
    S->>N: Generate Alerts
    
    N->>F: WebSocket Alert
    F->>U: Real-time Notification
    
    U->>F: View Stock Details
    F->>A: GET /stocks/{symbol}
    A->>D: Query Stock Data
    D-->>A: Stock Information
    A-->>F: Stock Details
    F->>U: Display Charts & Data
```

## 📁 Project Structure

```
EntryAlert/
├── backend/                    # FastAPI Backend Application
│   ├── app/
│   │   ├── __init__.py
│   │   ├── main.py            # FastAPI application entry point
│   │   ├── config.py          # Configuration settings
│   │   ├── database.py        # Database connection and setup
│   │   ├── models/            # SQLAlchemy models
│   │   │   ├── __init__.py
│   │   │   ├── user.py        # User model
│   │   │   ├── stock.py       # Stock model
│   │   │   ├── watchlist.py   # Watchlist model
│   │   │   └── alert.py       # Alert model
│   │   ├── schemas/           # Pydantic schemas
│   │   │   ├── __init__.py
│   │   │   ├── user.py
│   │   │   ├── stock.py
│   │   │   ├── watchlist.py
│   │   │   └── alert.py
│   │   ├── api/               # API routes
│   │   │   ├── __init__.py
│   │   │   ├── auth.py        # Authentication endpoints
│   │   │   ├── stocks.py      # Stock-related endpoints
│   │   │   ├── watchlists.py  # Watchlist endpoints
│   │   │   └── alerts.py      # Alert endpoints
│   │   ├── services/          # Business logic services
│   │   │   ├── __init__.py
│   │   │   ├── auth_service.py
│   │   │   ├── stock_service.py
│   │   │   ├── screener_service.py
│   │   │   ├── alert_service.py
│   │   │   └── market_data_service.py
│   │   ├── core/              # Core utilities
│   │   │   ├── __init__.py
│   │   │   ├── security.py    # JWT and password handling
│   │   │   ├── websocket.py   # WebSocket manager
│   │   │   └── background_tasks.py
│   │   ├── algorithms/        # Custom trading algorithms
│   │   │   ├── __init__.py
│   │   │   ├── technical_indicators.py
│   │   │   ├── screening_algorithms.py
│   │   │   └── entry_point_detector.py
│   │   └── utils/             # Utility functions
│   │       ├── __init__.py
│   │       ├── logger.py
│   │       └── helpers.py
│   ├── tests/                 # Backend tests
│   ├── requirements.txt       # Python dependencies
│   ├── .env.example          # Environment variables template
│   └── alembic/              # Database migrations
├── frontend/                  # React Frontend Application
│   ├── public/
│   │   ├── index.html
│   │   └── favicon.ico
│   ├── src/
│   │   ├── components/        # Reusable components
│   │   │   ├── common/
│   │   │   ├── charts/
│   │   │   ├── forms/
│   │   │   └── layout/
│   │   ├── pages/             # Page components
│   │   │   ├── Dashboard.tsx
│   │   │   ├── Screener.tsx
│   │   │   ├── Watchlist.tsx
│   │   │   ├── StockDetail.tsx
│   │   │   └── Settings.tsx
│   │   ├── hooks/             # Custom React hooks
│   │   ├── services/          # API service functions
│   │   ├── store/             # State management
│   │   ├── types/             # TypeScript type definitions
│   │   ├── utils/             # Utility functions
│   │   ├── App.tsx
│   │   └── index.tsx
│   ├── package.json
│   ├── tsconfig.json
│   └── tailwind.config.js
├── docs/                      # Documentation
├── docker-compose.yml         # Docker configuration
├── .gitignore
└── README.md
```

## 🛠️ Technology Stack

### Backend
- **Framework**: FastAPI (Python 3.9+)
- **Database**: PostgreSQL with SQLAlchemy ORM
- **Caching**: Redis for market data caching
- **Authentication**: JWT tokens
- **WebSocket**: FastAPI WebSocket support
- **Background Jobs**: APScheduler for periodic tasks
- **Market Data**: Alpha Vantage, Yahoo Finance APIs

### Frontend
- **Framework**: React 18 with TypeScript
- **Styling**: Tailwind CSS
- **Charts**: Chart.js / Recharts
- **State Management**: React Query + Context API
- **WebSocket**: Native WebSocket API
- **Build Tool**: Vite

### DevOps & Tools
- **Containerization**: Docker & Docker Compose
- **Database Migrations**: Alembic
- **Testing**: Pytest (Backend), Jest (Frontend)
- **Code Quality**: ESLint, Prettier, Black
- **CI/CD**: GitHub Actions

## 🚀 Quick Start Guide

### Prerequisites

Before you begin, ensure you have the following installed on your system:

- **Python 3.9 or higher** - [Download Python](https://www.python.org/downloads/)
- **Node.js 16 or higher** - [Download Node.js](https://nodejs.org/)
- **PostgreSQL 13 or higher** - [Download PostgreSQL](https://www.postgresql.org/download/)
- **Redis 6 or higher** - [Download Redis](https://redis.io/download)
- **Git** - [Download Git](https://git-scm.com/downloads)

### System Requirements

- **Operating System**: Windows 10+, macOS 10.15+, or Linux (Ubuntu 18.04+)
- **RAM**: Minimum 4GB, Recommended 8GB
- **Storage**: At least 2GB free space
- **Network**: Internet connection for API access

### Installation Instructions

#### Step 1: Clone the Repository

```bash
git clone https://github.com/your-username/EntryAlert.git
cd EntryAlert
```

#### Step 2: Database Setup

1. **Start PostgreSQL service** (if not already running)
2. **Create a database**:
   ```sql
   CREATE DATABASE entryalert;
   CREATE USER entryalert_user WITH PASSWORD 'your_password';
   GRANT ALL PRIVILEGES ON DATABASE entryalert TO entryalert_user;
   ```

3. **Start Redis service** (if not already running)

#### Step 3: Backend Setup

1. **Navigate to backend directory**:
   ```bash
   cd backend
   ```

2. **Create and activate virtual environment**:
   ```bash
   # On Windows
   python -m venv venv
   venv\Scripts\activate

   # On macOS/Linux
   python3 -m venv venv
   source venv/bin/activate
   ```

3. **Install Python dependencies**:
   ```bash
   pip install --upgrade pip
   pip install -r requirements.txt
   ```

4. **Configure environment variables**:
   ```bash
   cp .env.example .env
   ```

   Edit the `.env` file with your configuration:
   ```env
   # Database Configuration
   DATABASE_URL=postgresql://entryalert_user:your_password@localhost:5432/entryalert

   # Redis Configuration
   REDIS_URL=redis://localhost:6379

   # Security (Generate a secure secret key)
   SECRET_KEY=your-super-secret-key-change-this-in-production

   # API Keys (See API Setup section below)
   ALPHA_VANTAGE_API_KEY=your_alpha_vantage_api_key
   YAHOO_FINANCE_API_KEY=your_yahoo_finance_api_key

   # Email Configuration (Optional)
   SMTP_HOST=smtp.gmail.com
   SMTP_PORT=587
   SMTP_USER=<EMAIL>
   SMTP_PASSWORD=your_app_password
   FROM_EMAIL=<EMAIL>
   ```

5. **Run database migrations**:
   ```bash
   alembic upgrade head
   ```

6. **Start the backend server**:
   ```bash
   uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
   ```

   The backend API will be available at `http://localhost:8000`

#### Step 4: Frontend Setup

1. **Open a new terminal** and navigate to frontend directory:
   ```bash
   cd frontend
   ```

2. **Install Node.js dependencies**:
   ```bash
   npm install
   ```

3. **Configure environment variables**:
   Create a `.env` file in the frontend directory:
   ```env
   VITE_API_URL=http://localhost:8000/api/v1
   VITE_WS_URL=ws://localhost:8000/ws
   ```

4. **Start the frontend development server**:
   ```bash
   npm run dev
   ```

   The frontend will be available at `http://localhost:5173`

#### Step 5: Verify Installation

1. **Open your browser** and go to `http://localhost:5173`
2. **Register a new account** or use the test credentials
3. **Check the API documentation** at `http://localhost:8000/docs`

### Docker Setup (Alternative)

If you prefer using Docker:

```bash
# Clone the repository
git clone https://github.com/your-username/EntryAlert.git
cd EntryAlert

# Copy environment files
cp backend/.env.example backend/.env
# Edit backend/.env with your configuration

# Start all services
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

## 📊 Features Overview

### 🎯 Core Features

#### Stock Screening Engine
- **Real-time screening** across 8000+ US stocks
- **Custom filtering criteria**:
  - Price range filtering
  - Volume thresholds
  - Market capitalization limits
  - Sector and industry selection
- **Technical indicator filters**:
  - RSI (Relative Strength Index)
  - MACD (Moving Average Convergence Divergence)
  - Bollinger Bands
  - Moving averages (SMA, EMA)
  - Volume surge detection
- **Performance optimized** with Redis caching
- **Export results** to CSV or JSON

#### Intelligent Watchlist Management
- **Multiple watchlists** (up to 10 per user)
- **Personal notes** and target prices for each stock
- **Real-time price updates** via WebSocket
- **Performance tracking** with gain/loss calculations
- **Drag-and-drop reordering** of stocks
- **Bulk operations** (add/remove multiple stocks)
- **Watchlist sharing** with other users
- **Import/export** functionality

#### Advanced Alert System
- **Multiple alert types**:
  - Price above/below thresholds
  - Percentage change alerts
  - Volume surge notifications
  - Technical indicator signals (RSI, MACD)
  - Moving average crossovers
  - Support/resistance level breaks
- **Flexible notification channels**:
  - Email notifications
  - In-app notifications
  - WebSocket real-time alerts
- **Alert management**:
  - Pause/resume alerts
  - Bulk operations
  - Alert history and statistics
  - Custom alert templates

#### Market Data Integration
- **Multiple data sources**:
  - Alpha Vantage (primary)
  - Yahoo Finance (backup)
  - Automatic failover between sources
- **Real-time quotes** with 1-minute refresh
- **Historical data** with multiple timeframes
- **Rate limit management** and caching
- **Data validation** and error handling

### 🚀 Advanced Features

#### Technical Analysis
- **Custom algorithms** for entry point detection
- **Support/resistance level** identification
- **Trend analysis** with multiple timeframes
- **Volume analysis** and surge detection
- **Momentum indicators** and change alerts
- **Pattern recognition** (future enhancement)

#### User Experience
- **Responsive design** for desktop and mobile
- **Dark/light theme** support
- **Customizable dashboard** with drag-and-drop widgets
- **Keyboard shortcuts** for power users
- **Progressive Web App** (PWA) capabilities
- **Offline functionality** for cached data

#### Security & Performance
- **JWT authentication** with refresh tokens
- **Rate limiting** to prevent abuse
- **Input validation** and sanitization
- **SQL injection protection**
- **XSS prevention** measures
- **HTTPS enforcement** in production
- **Database connection pooling**
- **Redis caching** for performance
- **Background job processing**

#### API & Integration
- **RESTful API** with OpenAPI documentation
- **WebSocket API** for real-time updates
- **Webhook support** for external integrations
- **CSV/JSON export** capabilities
- **Third-party API integration** ready
- **Rate limiting** and quota management

### 📱 User Interface Features

#### Dashboard
- **Portfolio overview** with performance metrics
- **Recent alerts** and notifications
- **Market summary** with major indices
- **Quick actions** for common tasks
- **Customizable widgets** and layout

#### Stock Detail Pages
- **Comprehensive stock information**
- **Interactive price charts** with technical indicators
- **Historical performance** analysis
- **News and events** integration (future)
- **Peer comparison** tools
- **Financial metrics** and ratios

#### Screening Interface
- **Intuitive filter builder**
- **Real-time result updates**
- **Sortable result tables**
- **Bulk actions** on results
- **Save screening criteria** as templates
- **Historical screening** results

#### Settings & Preferences
- **Profile management**
- **Notification preferences**
- **Theme customization**
- **API key management**
- **Data export/import** tools
- **Account security** settings

### 🔧 Administrative Features

#### User Management
- **User registration** and authentication
- **Profile management**
- **Usage analytics** and monitoring
- **Rate limiting** per user
- **Subscription management** (future)

#### System Monitoring
- **Health check endpoints**
- **Performance metrics**
- **Error tracking** and logging
- **Database monitoring**
- **API usage statistics**
- **Background job monitoring**

### 🌟 Upcoming Features

#### Enhanced Analytics
- **Portfolio backtesting**
- **Risk analysis** tools
- **Performance attribution**
- **Correlation analysis**
- **Monte Carlo simulations**

#### Social Features
- **Community watchlists**
- **User-generated content**
- **Discussion forums**
- **Expert insights**
- **Social trading** features

#### Advanced Integrations
- **Broker API integration**
- **News sentiment analysis**
- **Economic calendar**
- **Earnings calendar**
- **Options data** integration

### 📊 Technical Specifications

#### Performance Metrics
- **Response time**: < 200ms for cached data
- **Throughput**: 1000+ requests per minute
- **Uptime**: 99.9% availability target
- **Data freshness**: 1-minute market data updates
- **Scalability**: Horizontal scaling ready

#### Data Coverage
- **8000+ US stocks** (NYSE, NASDAQ)
- **Real-time quotes** during market hours
- **Historical data** up to 20 years
- **Technical indicators** with customizable periods
- **Fundamental data** (P/E, EPS, etc.)

#### Browser Support
- **Chrome** 90+
- **Firefox** 88+
- **Safari** 14+
- **Edge** 90+
- **Mobile browsers** (iOS Safari, Chrome Mobile)

## 🔑 API Keys Setup

EntryAlert uses free APIs for market data. You'll need to obtain API keys from the following providers:

### Alpha Vantage (Primary Data Source)

1. **Visit** [Alpha Vantage](https://www.alphavantage.co/support/#api-key)
2. **Click "Get your free API key today"**
3. **Fill out the form** with your information
4. **Copy the API key** and add it to your `.env` file:
   ```env
   ALPHA_VANTAGE_API_KEY=your_alpha_vantage_api_key
   ```

**Rate Limits**: 5 API requests per minute, 500 requests per day (free tier)

### Yahoo Finance (Backup Data Source)

Yahoo Finance API is used as a fallback and doesn't require an API key for basic functionality. However, for enhanced features:

1. **Visit** [RapidAPI Yahoo Finance](https://rapidapi.com/apidojo/api/yahoo-finance1/)
2. **Subscribe to the free plan**
3. **Copy the API key** and add it to your `.env` file:
   ```env
   YAHOO_FINANCE_API_KEY=your_yahoo_finance_api_key
   ```

**Rate Limits**: 500 requests per month (free tier)

### Email Configuration (Optional)

For email notifications, configure SMTP settings:

#### Gmail Setup:
1. **Enable 2-Factor Authentication** on your Gmail account
2. **Generate an App Password**:
   - Go to Google Account settings
   - Security → 2-Step Verification → App passwords
   - Generate password for "Mail"
3. **Add to `.env` file**:
   ```env
   SMTP_HOST=smtp.gmail.com
   SMTP_PORT=587
   SMTP_USER=<EMAIL>
   SMTP_PASSWORD=your_app_password
   FROM_EMAIL=<EMAIL>
   ```

#### Other Email Providers:
- **Outlook**: `smtp-mail.outlook.com:587`
- **Yahoo**: `smtp.mail.yahoo.com:587`
- **Custom SMTP**: Use your provider's settings

## 🔧 Configuration

### Environment Variables Reference

#### Backend Configuration (`backend/.env`)

```env
# Application Settings
APP_NAME=EntryAlert
APP_VERSION=1.0.0
DEBUG=false
ENVIRONMENT=development

# Database Configuration
DATABASE_URL=postgresql://entryalert_user:password@localhost:5432/entryalert
TEST_DATABASE_URL=postgresql://entryalert_user:password@localhost:5432/entryalert_test

# Redis Configuration
REDIS_URL=redis://localhost:6379

# Security Settings
SECRET_KEY=your-super-secret-key-change-this-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# API Keys
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_api_key
YAHOO_FINANCE_API_KEY=your_yahoo_finance_api_key

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_app_password
FROM_EMAIL=<EMAIL>

# CORS Settings (comma-separated)
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:5173

# Rate Limiting
RATE_LIMIT_PER_MINUTE=100

# Background Tasks
SCREENING_INTERVAL_MINUTES=5
MARKET_DATA_UPDATE_INTERVAL_MINUTES=1
ALERT_CHECK_INTERVAL_MINUTES=1

# Logging
LOG_LEVEL=INFO
LOG_FORMAT=json

# Cache Settings
CACHE_TTL_SECONDS=300
MARKET_DATA_CACHE_TTL=60

# Business Limits
MAX_WATCHLISTS_PER_USER=10
MAX_ALERTS_PER_USER=100
MAX_STOCKS_PER_WATCHLIST=50
```

#### Frontend Configuration (`frontend/.env`)

```env
# API Configuration
VITE_API_URL=http://localhost:8000/api/v1
VITE_WS_URL=ws://localhost:8000/ws

# Application Settings
VITE_APP_NAME=EntryAlert
VITE_APP_VERSION=1.0.0

# Feature Flags
VITE_ENABLE_ANALYTICS=false
VITE_ENABLE_ERROR_REPORTING=false
```

## 📖 Usage Examples

### Basic Usage

#### 1. User Registration and Login

```bash
# Register a new user
curl -X POST "http://localhost:8000/api/v1/auth/register" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "username": "testuser",
    "password": "SecurePass123",
    "first_name": "John",
    "last_name": "Doe"
  }'

# Login
curl -X POST "http://localhost:8000/api/v1/auth/login" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=testuser&password=SecurePass123"
```

#### 2. Stock Search and Information

```bash
# Search for stocks
curl -X GET "http://localhost:8000/api/v1/stocks/search?q=AAPL" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

# Get stock details
curl -X GET "http://localhost:8000/api/v1/stocks/AAPL" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

# Get historical data
curl -X GET "http://localhost:8000/api/v1/stocks/AAPL/history?timeframe=1d&limit=30" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

#### 3. Stock Screening

```bash
# Screen stocks with criteria
curl -X POST "http://localhost:8000/api/v1/stocks/screen" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "min_price": 10,
    "max_price": 100,
    "min_volume": 1000000,
    "sectors": ["Technology", "Healthcare"],
    "rsi_min": 30,
    "rsi_max": 70
  }'
```

#### 4. Watchlist Management

```bash
# Create a watchlist
curl -X POST "http://localhost:8000/api/v1/watchlists" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "name": "Tech Stocks",
    "description": "My favorite technology stocks"
  }'

# Add stock to watchlist
curl -X POST "http://localhost:8000/api/v1/watchlists/1/items" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "stock_id": 1,
    "notes": "Strong buy candidate",
    "target_price": 150.00
  }'
```

### Frontend Usage

#### 1. Stock Screening Interface

1. **Navigate to the Screener page**
2. **Set your criteria**:
   - Price range: $10 - $100
   - Volume: > 1M shares
   - Sectors: Technology, Healthcare
   - RSI: 30-70
3. **Click "Run Screen"**
4. **Review results** and add stocks to watchlist

#### 2. Creating Alerts

1. **Go to a stock detail page**
2. **Click "Create Alert"**
3. **Choose alert type**:
   - Price above/below
   - Volume surge
   - RSI oversold/overbought
4. **Set conditions** and notification preferences
5. **Save alert**

#### 3. Watchlist Management

1. **Create a new watchlist**
2. **Add stocks** by searching or from screening results
3. **Set target prices** and notes
4. **Monitor performance** on the dashboard

## 📈 API Documentation

### Interactive Documentation

Once the backend is running, visit:
- **Swagger UI**: `http://localhost:8000/docs`
- **ReDoc**: `http://localhost:8000/redoc`

### Authentication

All API endpoints (except registration and login) require JWT authentication:

```bash
# Include in headers
Authorization: Bearer YOUR_JWT_TOKEN
```

### Rate Limits

- **General endpoints**: 100 requests per minute
- **Authentication endpoints**: 10 requests per minute
- **Market data endpoints**: Limited by external API providers

### Response Format

All API responses follow this structure:

```json
{
  "data": { ... },
  "status_code": 200,
  "timestamp": **********
}
```

Error responses:

```json
{
  "error": "Error message",
  "status_code": 400,
  "timestamp": **********,
  "path": "/api/v1/endpoint",
  "details": { ... }
}
```

## 🔧 Troubleshooting

### Common Issues and Solutions

#### Backend Issues

**1. Database Connection Error**
```
sqlalchemy.exc.OperationalError: (psycopg2.OperationalError) could not connect to server
```

**Solutions:**
- Ensure PostgreSQL is running: `sudo service postgresql start`
- Check database credentials in `.env` file
- Verify database exists: `psql -U postgres -c "\l"`
- Create database if missing:
  ```sql
  CREATE DATABASE entryalert;
  ```

**2. Redis Connection Error**
```
redis.exceptions.ConnectionError: Error 111 connecting to localhost:6379
```

**Solutions:**
- Start Redis server: `redis-server` or `sudo service redis-server start`
- Check Redis is running: `redis-cli ping` (should return "PONG")
- Verify Redis URL in `.env` file

**3. API Key Errors**
```
Market data service unavailable
```

**Solutions:**
- Verify API keys are correctly set in `.env` file
- Check API key validity at provider websites
- Ensure no extra spaces or quotes around API keys
- Check rate limits haven't been exceeded

**4. Migration Errors**
```
alembic.util.exc.CommandError: Can't locate revision identified by 'head'
```

**Solutions:**
```bash
# Reset migrations
rm -rf alembic/versions/*
alembic revision --autogenerate -m "Initial migration"
alembic upgrade head
```

**5. Import Errors**
```
ModuleNotFoundError: No module named 'app'
```

**Solutions:**
- Ensure virtual environment is activated
- Reinstall dependencies: `pip install -r requirements.txt`
- Check Python path: `export PYTHONPATH="${PYTHONPATH}:$(pwd)"`

#### Frontend Issues

**1. API Connection Error**
```
Network Error: Request failed with status code 404
```

**Solutions:**
- Ensure backend is running on `http://localhost:8000`
- Check `VITE_API_URL` in frontend `.env` file
- Verify CORS settings in backend configuration

**2. Build Errors**
```
Module not found: Can't resolve '@/components/...'
```

**Solutions:**
- Check `tsconfig.json` path mapping
- Ensure all dependencies are installed: `npm install`
- Clear node_modules and reinstall: `rm -rf node_modules && npm install`

**3. WebSocket Connection Issues**
```
WebSocket connection failed
```

**Solutions:**
- Check `VITE_WS_URL` in frontend `.env` file
- Ensure backend WebSocket endpoint is accessible
- Check firewall settings

#### General Issues

**1. Port Already in Use**
```
Error: listen EADDRINUSE: address already in use :::8000
```

**Solutions:**
```bash
# Find process using port
lsof -i :8000  # or netstat -tulpn | grep :8000

# Kill process
kill -9 <PID>

# Or use different port
uvicorn app.main:app --port 8001
```

**2. Permission Denied Errors**
```
PermissionError: [Errno 13] Permission denied
```

**Solutions:**
- Check file permissions: `chmod +x script.sh`
- Run with appropriate user permissions
- On Windows, run terminal as Administrator

**3. Environment Variables Not Loading**

**Solutions:**
- Ensure `.env` file is in correct directory
- Check file encoding (should be UTF-8)
- Restart application after changing `.env`
- Verify no spaces around `=` in `.env` file

### Performance Issues

**1. Slow API Responses**

**Solutions:**
- Check database query performance
- Verify Redis cache is working
- Monitor external API rate limits
- Consider upgrading to paid API tiers

**2. High Memory Usage**

**Solutions:**
- Monitor background tasks
- Check for memory leaks in long-running processes
- Adjust cache TTL settings
- Consider horizontal scaling

### Getting Help

If you encounter issues not covered here:

1. **Check the logs**:
   ```bash
   # Backend logs
   tail -f backend/logs/app.log

   # Frontend console
   Open browser dev tools → Console tab
   ```

2. **Enable debug mode**:
   ```env
   # In backend/.env
   DEBUG=true
   LOG_LEVEL=DEBUG
   ```

3. **Test API endpoints**:
   - Use Swagger UI at `http://localhost:8000/docs`
   - Test with curl or Postman

4. **Check system resources**:
   ```bash
   # Check disk space
   df -h

   # Check memory usage
   free -h

   # Check running processes
   ps aux | grep python
   ```

5. **Create an issue** on GitHub with:
   - Error message and stack trace
   - Steps to reproduce
   - System information (OS, Python version, Node.js version)
   - Configuration (without sensitive data)

## 🧪 Testing

### Backend Tests
```bash
cd backend

# Run all tests
pytest

# Run with coverage
pytest --cov=app

# Run specific test file
pytest tests/test_stocks.py

# Run with verbose output
pytest -v
```

### Frontend Tests
```bash
cd frontend

# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test -- --coverage
```

### Integration Tests
```bash
# Start test environment
docker-compose -f docker-compose.test.yml up -d

# Run integration tests
pytest tests/integration/

# Cleanup
docker-compose -f docker-compose.test.yml down
```

## 🚀 Production Deployment

### Prerequisites for Production

- **Domain name** with SSL certificate
- **Production database** (PostgreSQL)
- **Production Redis** instance
- **Email service** (SMTP or service like SendGrid)
- **Monitoring tools** (optional but recommended)

### Environment Setup

#### 1. Production Environment Variables

Create production `.env` files:

**Backend (`backend/.env.prod`)**:
```env
# Application
APP_NAME=EntryAlert
APP_VERSION=1.0.0
DEBUG=false
ENVIRONMENT=production

# Database (use production credentials)
DATABASE_URL=***************************************************/entryalert
REDIS_URL=redis://prod-redis:6379

# Security (use strong, unique values)
SECRET_KEY=your-super-secure-production-secret-key
ACCESS_TOKEN_EXPIRE_MINUTES=30

# API Keys (production keys with higher limits)
ALPHA_VANTAGE_API_KEY=your_production_alpha_vantage_key
YAHOO_FINANCE_API_KEY=your_production_yahoo_finance_key

# Email (production SMTP)
SMTP_HOST=smtp.sendgrid.net
SMTP_PORT=587
SMTP_USER=apikey
SMTP_PASSWORD=your_sendgrid_api_key
FROM_EMAIL=<EMAIL>

# CORS (your production domains)
ALLOWED_ORIGINS=https://yourdomain.com,https://www.yourdomain.com

# Logging
LOG_LEVEL=INFO
LOG_FORMAT=json

# Performance
CACHE_TTL_SECONDS=300
MARKET_DATA_CACHE_TTL=60
```

**Frontend (`frontend/.env.prod`)**:
```env
VITE_API_URL=https://api.yourdomain.com/api/v1
VITE_WS_URL=wss://api.yourdomain.com/ws
VITE_APP_NAME=EntryAlert
VITE_APP_VERSION=1.0.0
```

#### 2. Docker Production Deployment

Create `docker-compose.prod.yml`:

```yaml
version: '3.8'

services:
  db:
    image: postgres:13
    environment:
      POSTGRES_DB: entryalert
      POSTGRES_USER: prod_user
      POSTGRES_PASSWORD: secure_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped

  redis:
    image: redis:6-alpine
    restart: unless-stopped

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.prod
    env_file:
      - ./backend/.env.prod
    depends_on:
      - db
      - redis
    restart: unless-stopped

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.prod
    env_file:
      - ./frontend/.env.prod
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - backend
      - frontend
    restart: unless-stopped

volumes:
  postgres_data:
```

#### 3. Nginx Configuration

Create `nginx.conf`:

```nginx
events {
    worker_connections 1024;
}

http {
    upstream backend {
        server backend:8000;
    }

    upstream frontend {
        server frontend:3000;
    }

    # Redirect HTTP to HTTPS
    server {
        listen 80;
        server_name yourdomain.com www.yourdomain.com;
        return 301 https://$server_name$request_uri;
    }

    # HTTPS server
    server {
        listen 443 ssl http2;
        server_name yourdomain.com www.yourdomain.com;

        ssl_certificate /etc/nginx/ssl/cert.pem;
        ssl_certificate_key /etc/nginx/ssl/key.pem;

        # Frontend
        location / {
            proxy_pass http://frontend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }

        # Backend API
        location /api/ {
            proxy_pass http://backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
        }

        # WebSocket
        location /ws/ {
            proxy_pass http://backend;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
        }
    }
}
```

#### 4. Deploy to Production

```bash
# 1. Clone repository on production server
git clone https://github.com/your-username/EntryAlert.git
cd EntryAlert

# 2. Set up environment files
cp backend/.env.example backend/.env.prod
cp frontend/.env.example frontend/.env.prod
# Edit with production values

# 3. Build and start services
docker-compose -f docker-compose.prod.yml up -d --build

# 4. Run database migrations
docker-compose -f docker-compose.prod.yml exec backend alembic upgrade head

# 5. Check logs
docker-compose -f docker-compose.prod.yml logs -f
```

### Cloud Deployment Options

#### AWS Deployment
- **ECS/Fargate** for containerized deployment
- **RDS** for PostgreSQL database
- **ElastiCache** for Redis
- **ALB** for load balancing
- **Route 53** for DNS
- **CloudFront** for CDN

#### Google Cloud Platform
- **Cloud Run** for serverless containers
- **Cloud SQL** for PostgreSQL
- **Memorystore** for Redis
- **Cloud Load Balancing**
- **Cloud CDN**

#### DigitalOcean
- **App Platform** for easy deployment
- **Managed Databases** for PostgreSQL and Redis
- **Spaces** for static file storage
- **Load Balancers**

### Monitoring and Maintenance

#### Health Checks
```bash
# Check application health
curl https://yourdomain.com/health

# Check API status
curl https://yourdomain.com/api/v1/health
```

#### Log Monitoring
```bash
# View application logs
docker-compose -f docker-compose.prod.yml logs backend

# Monitor in real-time
docker-compose -f docker-compose.prod.yml logs -f --tail=100
```

#### Database Backup
```bash
# Create backup
docker-compose -f docker-compose.prod.yml exec db pg_dump -U prod_user entryalert > backup.sql

# Restore backup
docker-compose -f docker-compose.prod.yml exec -T db psql -U prod_user entryalert < backup.sql
```

#### Updates and Maintenance
```bash
# Update application
git pull origin main
docker-compose -f docker-compose.prod.yml up -d --build

# Run migrations
docker-compose -f docker-compose.prod.yml exec backend alembic upgrade head

# Restart services
docker-compose -f docker-compose.prod.yml restart
```

## 📝 Contributing

We welcome contributions from the community! Here's how you can help:

### Development Setup

1. **Fork the repository** on GitHub
2. **Clone your fork**:
   ```bash
   git clone https://github.com/your-username/EntryAlert.git
   cd EntryAlert
   ```
3. **Create a feature branch**:
   ```bash
   git checkout -b feature/your-feature-name
   ```
4. **Set up development environment** (follow installation instructions above)
5. **Make your changes** and test thoroughly
6. **Add tests** for new functionality
7. **Run the test suite**:
   ```bash
   # Backend tests
   cd backend && pytest

   # Frontend tests
   cd frontend && npm test
   ```
8. **Submit a pull request** with a clear description

### Contribution Guidelines

#### Code Style
- **Python**: Follow PEP 8, use Black for formatting
- **TypeScript/React**: Follow Airbnb style guide, use Prettier
- **Commit messages**: Use conventional commits format
- **Documentation**: Update README and API docs for new features

#### Pull Request Process
1. **Update documentation** for any new features
2. **Add tests** with good coverage
3. **Ensure CI passes** all checks
4. **Request review** from maintainers
5. **Address feedback** promptly

#### Types of Contributions
- 🐛 **Bug fixes**
- ✨ **New features**
- 📚 **Documentation improvements**
- 🎨 **UI/UX enhancements**
- ⚡ **Performance optimizations**
- 🧪 **Test coverage improvements**

### Development Guidelines

#### Backend Development
- Use **FastAPI** best practices
- Implement **proper error handling**
- Add **comprehensive logging**
- Write **unit and integration tests**
- Follow **RESTful API** conventions

#### Frontend Development
- Use **TypeScript** for type safety
- Implement **responsive design**
- Add **error boundaries**
- Write **component tests**
- Follow **accessibility** guidelines

#### Database Changes
- Create **Alembic migrations** for schema changes
- Test migrations on **sample data**
- Document **breaking changes**
- Consider **backward compatibility**

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

### MIT License Summary
- ✅ **Commercial use** allowed
- ✅ **Modification** allowed
- ✅ **Distribution** allowed
- ✅ **Private use** allowed
- ❌ **Liability** not provided
- ❌ **Warranty** not provided

## 🤝 Support

### Getting Help

1. **Check the documentation** - Most questions are answered here
2. **Search existing issues** - Your question might already be answered
3. **Create a new issue** - Use the appropriate template
4. **Join discussions** - Participate in GitHub Discussions

### Issue Templates

When creating issues, please use the appropriate template:

- 🐛 **Bug Report** - For reporting bugs
- ✨ **Feature Request** - For suggesting new features
- 📚 **Documentation** - For documentation improvements
- ❓ **Question** - For general questions

### Community Guidelines

- Be **respectful** and **inclusive**
- Provide **clear descriptions** and **reproduction steps**
- Use **appropriate labels** and **templates**
- **Search before posting** to avoid duplicates
- **Help others** when you can

### Commercial Support

For commercial support, custom development, or enterprise features:
- 📧 Email: <EMAIL>
- 💼 LinkedIn: [EntryAlert](https://linkedin.com/company/entryalert)
- 🌐 Website: [www.entryalert.com](https://www.entryalert.com)

## 🙏 Acknowledgments

### Technologies Used
- **FastAPI** - Modern Python web framework
- **React** - Frontend library
- **PostgreSQL** - Database
- **Redis** - Caching and sessions
- **Alpha Vantage** - Market data API
- **Yahoo Finance** - Backup market data
- **Tailwind CSS** - Styling framework

### Contributors
Thanks to all contributors who have helped make this project better!

### Inspiration
This project was inspired by the need for accessible, powerful stock screening tools for individual investors.

## 📊 Project Status

### Current Version: 1.0.0

#### ✅ Completed Features
- User authentication and authorization
- Stock screening with multiple criteria
- Watchlist management
- Real-time alerts system
- Market data integration
- Responsive web interface
- API documentation
- Docker containerization

#### 🚧 In Development
- Mobile app (React Native)
- Advanced charting features
- Portfolio tracking
- Social features
- News integration

#### 📋 Roadmap
- **Q1 2024**: Mobile app release
- **Q2 2024**: Advanced analytics
- **Q3 2024**: Social trading features
- **Q4 2024**: Broker integrations

### Statistics
- **8000+** US stocks supported
- **100+** commits
- **50+** tests
- **99.9%** uptime target
- **<200ms** average response time

---

## 🌟 Star History

If you find this project useful, please consider giving it a star ⭐ on GitHub!

[![Star History Chart](https://api.star-history.com/svg?repos=your-username/EntryAlert&type=Date)](https://star-history.com/#your-username/EntryAlert&Date)

---

**Built with ❤️ for the trading community**

*EntryAlert - Empowering individual investors with professional-grade stock screening tools*
