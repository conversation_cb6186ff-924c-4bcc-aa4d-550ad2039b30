"""
Background tasks for market data updates and alert processing.
"""
import asyncio
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.interval import IntervalTrigger

from ..config import settings
from ..database import SessionLocal, get_redis
from ..models.stock import Stock
from ..models.alert import <PERSON><PERSON>, AlertStatus, AlertNotification
from ..models.user import User
from ..services.market_data_service import market_data_service
from ..services.email_service import email_service
from ..services.push_service import push_service
from ..services.cache_service import cache_service
from ..algorithms.technical_indicators import calculate_all_indicators, generate_trading_signals
from ..core.websocket import websocket_manager
from ..utils.logger import get_logger
from ..utils.monitoring import monitor_background_task
from ..utils.retry import retry_async, BACKGROUND_TASK_RETRY_CONFIG, NETWORK_RETRY_CONFIG, CircuitBreaker

logger = get_logger(__name__)


class BackgroundTaskManager:
    """Manages background tasks for the application."""

    def __init__(self):
        self.scheduler = AsyncIOScheduler()
        self.is_running = False

        # Circuit breakers for external services
        self.market_data_circuit_breaker = CircuitBreaker(
            failure_threshold=5,
            recovery_timeout=300.0  # 5 minutes
        )
    
    async def start(self):
        """Start background tasks."""
        if self.is_running:
            return
        
        logger.info("Starting background tasks...")
        
        # Schedule market data updates
        self.scheduler.add_job(
            self.update_market_data,
            IntervalTrigger(minutes=settings.MARKET_DATA_UPDATE_INTERVAL_MINUTES),
            id='market_data_update',
            replace_existing=True
        )
        
        # Schedule alert checking
        self.scheduler.add_job(
            self.check_alerts,
            IntervalTrigger(minutes=settings.ALERT_CHECK_INTERVAL_MINUTES),
            id='alert_check',
            replace_existing=True
        )
        
        # Schedule stock screening
        self.scheduler.add_job(
            self.run_stock_screening,
            IntervalTrigger(minutes=settings.SCREENING_INTERVAL_MINUTES),
            id='stock_screening',
            replace_existing=True
        )

        # Schedule data cleanup (daily at 2 AM)
        self.scheduler.add_job(
            self.cleanup_old_data,
            IntervalTrigger(hours=24),
            id='data_cleanup',
            replace_existing=True
        )

        # Schedule cache cleanup (every 6 hours)
        self.scheduler.add_job(
            self.cleanup_cache,
            IntervalTrigger(hours=6),
            id='cache_cleanup',
            replace_existing=True
        )
        
        # Start scheduler
        self.scheduler.start()
        self.is_running = True
        
        logger.info("Background tasks started successfully")
    
    async def stop(self):
        """Stop background tasks."""
        if not self.is_running:
            return
        
        logger.info("Stopping background tasks...")
        self.scheduler.shutdown()
        await market_data_service.close_session()
        self.is_running = False
        logger.info("Background tasks stopped")
    
    @monitor_background_task("market_data_update")
    async def update_market_data(self):
        """Update market data for all active stocks."""
        logger.info("Starting market data update...")
        
        db = SessionLocal()
        try:
            # Get all active stocks
            stocks = db.query(Stock).filter(Stock.is_active == True).limit(100).all()
            
            if not stocks:
                logger.info("No active stocks found")
                return
            
            # Get symbols
            symbols = [stock.symbol for stock in stocks]
            
            # Fetch quotes in batches to respect rate limits
            batch_size = 10
            updated_count = 0
            
            for i in range(0, len(symbols), batch_size):
                batch_symbols = symbols[i:i + batch_size]
                
                try:
                    quotes = await self._fetch_quotes_with_retry(batch_symbols)
                    
                    for symbol, quote_data in quotes.items():
                        if quote_data:
                            # Find the stock in database
                            stock = next((s for s in stocks if s.symbol == symbol), None)
                            if stock:
                                # Update stock data
                                stock.current_price = quote_data.get('current_price')
                                stock.previous_close = quote_data.get('previous_close')
                                stock.open_price = quote_data.get('open_price')
                                stock.day_high = quote_data.get('day_high')
                                stock.day_low = quote_data.get('day_low')
                                stock.volume = quote_data.get('volume')
                                stock.market_cap = quote_data.get('market_cap')

                                # Cache the updated stock data
                                await cache_service.set_stock_data(symbol, quote_data)
                                stock.pe_ratio = quote_data.get('pe_ratio')
                                stock.eps = quote_data.get('eps')
                                stock.dividend_yield = quote_data.get('dividend_yield')
                                stock.beta = quote_data.get('beta')
                                
                                # Calculate price change
                                if stock.current_price and stock.previous_close:
                                    stock.price_change = stock.current_price - stock.previous_close
                                    stock.price_change_percent = (stock.price_change / stock.previous_close) * 100
                                
                                stock.last_data_update = datetime.utcnow()
                                updated_count += 1
                                
                                # Send real-time update via WebSocket
                                await websocket_manager.send_stock_update(stock.to_dict())
                    
                    # Commit batch
                    db.commit()
                    
                    # Small delay between batches to respect rate limits
                    await asyncio.sleep(1)
                    
                except Exception as e:
                    logger.error(f"Error updating batch {i//batch_size + 1}: {e}")
                    db.rollback()
            
            logger.info(f"Market data update completed. Updated {updated_count} stocks.")
            
        except Exception as e:
            logger.error(f"Error in market data update: {e}")
            db.rollback()
        finally:
            db.close()
    
    @monitor_background_task("alert_check")
    async def check_alerts(self):
        """Check and trigger alerts based on current market conditions."""
        logger.info("Starting alert check...")
        
        db = SessionLocal()
        try:
            # Get all active alerts
            alerts = db.query(Alert).filter(
                Alert.status == AlertStatus.ACTIVE
            ).join(Stock).all()
            
            triggered_count = 0
            
            for alert in alerts:
                try:
                    # Get current stock data
                    stock = alert.stock
                    if not stock or not stock.current_price:
                        continue
                    
                    # Check alert conditions
                    should_trigger = await self._check_alert_condition(alert, stock)
                    
                    if should_trigger:
                        # Trigger alert
                        alert.status = AlertStatus.TRIGGERED
                        alert.triggered_at = datetime.utcnow()
                        alert.trigger_price = stock.current_price
                        
                        # Send notification
                        await self._send_alert_notification(alert)
                        
                        triggered_count += 1
                        
                        logger.info(f"Alert {alert.id} triggered for {stock.symbol}")
                
                except Exception as e:
                    logger.error(f"Error checking alert {alert.id}: {e}")
            
            db.commit()
            logger.info(f"Alert check completed. Triggered {triggered_count} alerts.")
            
        except Exception as e:
            logger.error(f"Error in alert check: {e}")
            db.rollback()
        finally:
            db.close()

    @retry_async(exceptions=(Exception,), config=NETWORK_RETRY_CONFIG)
    async def _fetch_quotes_with_retry(self, symbols: List[str]) -> Dict[str, Any]:
        """Fetch quotes with retry logic and circuit breaker."""
        return await self.market_data_circuit_breaker(
            market_data_service.get_multiple_quotes
        )(symbols)

    @retry_async(exceptions=(Exception,), config=BACKGROUND_TASK_RETRY_CONFIG)
    async def _get_historical_data_with_retry(self, symbol: str, **kwargs) -> List[Dict[str, Any]]:
        """Get historical data with retry logic."""
        return await market_data_service.get_historical_data(symbol, **kwargs)

    async def _check_alert_condition(self, alert: Alert, stock: Stock) -> bool:
        """Check if an alert condition is met."""
        conditions = alert.conditions
        alert_type = alert.alert_type

        current_price = stock.current_price
        if not current_price:
            return False

        # Price-based alerts
        if alert_type.value == 'price_above':
            target_price = conditions.get('target_price')
            return target_price and current_price > target_price

        elif alert_type.value == 'price_below':
            target_price = conditions.get('target_price')
            return target_price and current_price < target_price

        # Volume-based alerts
        elif alert_type.value == 'volume_surge':
            if not stock.volume or not stock.avg_volume:
                return False
            threshold = conditions.get('threshold', 2.0)
            return stock.volume > (stock.avg_volume * threshold)

        # Technical indicator alerts - require historical data
        elif alert_type.value in ['rsi_oversold', 'rsi_overbought', 'macd_bullish', 'macd_bearish',
                                  'bollinger_squeeze', 'support_break', 'resistance_break', 'momentum_change']:
            return await self._check_technical_alert_condition(alert, stock)

        # Entry point alerts
        elif alert_type.value == 'entry_point':
            return await self._check_entry_point_condition(alert, stock)

        return False

    async def _check_technical_alert_condition(self, alert: Alert, stock: Stock) -> bool:
        """Check technical indicator alert conditions."""
        try:
            # Get historical data for the stock
            historical_data = await self._get_stock_historical_data(stock.symbol, days=50)
            if not historical_data or len(historical_data) < 20:
                logger.warning(f"Insufficient historical data for {stock.symbol}")
                return False

            # Calculate technical indicators
            indicators = calculate_all_indicators(historical_data)
            if not indicators:
                return False

            conditions = alert.conditions
            alert_type = alert.alert_type.value

            # RSI alerts
            if alert_type == 'rsi_oversold':
                rsi_threshold = conditions.get('rsi_threshold', 30)
                return indicators.get('rsi', 50) < rsi_threshold

            elif alert_type == 'rsi_overbought':
                rsi_threshold = conditions.get('rsi_threshold', 70)
                return indicators.get('rsi', 50) > rsi_threshold

            # MACD alerts
            elif alert_type == 'macd_bullish':
                macd = indicators.get('macd', 0)
                signal = indicators.get('macd_signal', 0)
                return macd > signal and macd > 0

            elif alert_type == 'macd_bearish':
                macd = indicators.get('macd', 0)
                signal = indicators.get('macd_signal', 0)
                return macd < signal and macd < 0

            # Bollinger Bands alerts
            elif alert_type == 'bollinger_squeeze':
                bb_upper = indicators.get('bb_upper', 0)
                bb_lower = indicators.get('bb_lower', 0)
                bb_width = bb_upper - bb_lower
                avg_width = conditions.get('avg_width', bb_width * 1.5)
                return bb_width < avg_width

            # Support/Resistance alerts
            elif alert_type == 'support_break':
                support_level = indicators.get('support', 0)
                return stock.current_price < support_level * 0.99  # 1% break below support

            elif alert_type == 'resistance_break':
                resistance_level = indicators.get('resistance', 0)
                return stock.current_price > resistance_level * 1.01  # 1% break above resistance

            # Momentum alerts
            elif alert_type == 'momentum_change':
                # Check if RSI is changing direction
                rsi = indicators.get('rsi', 50)
                momentum_threshold = conditions.get('momentum_threshold', 50)
                return abs(rsi - momentum_threshold) > 10

            return False

        except Exception as e:
            logger.error(f"Error checking technical alert condition: {e}")
            return False

    async def _check_entry_point_condition(self, alert: Alert, stock: Stock) -> bool:
        """Check entry point alert conditions using entry point detector."""
        try:
            # Get historical data for entry point analysis
            historical_data = await self._get_stock_historical_data(stock.symbol, days=100)
            if not historical_data or len(historical_data) < 50:
                return False

            # Use entry point detector
            from ..algorithms.entry_point_detector import EntryPointDetector
            detector = EntryPointDetector()

            entry_signals = detector.detect_entry_points(historical_data)

            # Check if any entry signals are triggered
            conditions = alert.conditions
            signal_types = conditions.get('signal_types', ['bullish_divergence', 'oversold_bounce', 'breakout_entry'])

            for signal_type in signal_types:
                if entry_signals.get(signal_type, {}).get('signal', False):
                    return True

            return False

        except Exception as e:
            logger.error(f"Error checking entry point condition: {e}")
            return False

    async def _get_stock_historical_data(self, symbol: str, days: int = 50) -> List[Dict[str, Any]]:
        """Get historical data for a stock with caching."""
        try:
            # Try to get from cache first
            cached_data = await cache_service.get_historical_data(symbol, days=days)
            if cached_data:
                logger.debug(f"Using cached historical data for {symbol}")
                return cached_data

            # Fetch fresh data if not in cache
            historical_data = await market_data_service.get_historical_data(symbol, days=days)

            # Cache the data for future use
            if historical_data:
                await cache_service.set_historical_data(symbol, historical_data, days=days)

            return historical_data

        except Exception as e:
            logger.error(f"Error getting historical data for {symbol}: {e}")
            return []

    async def _send_alert_notification(self, alert: Alert):
        """Send alert notification to user."""
        try:
            # Get user for notification preferences
            db = SessionLocal()
            try:
                user = db.query(User).filter(User.id == alert.user_id).first()
                if not user:
                    logger.error(f"User {alert.user_id} not found for alert {alert.id}")
                    return

                # Send WebSocket notification
                await websocket_manager.send_alert_notification(
                    alert.to_dict(),
                    alert.user_id
                )

                # Send email notification if enabled
                if user.notification_email:
                    await email_service.send_alert_notification(alert, user)

                # Send push notification if enabled
                if user.notification_push:
                    await push_service.send_alert_notification(alert, user)

            finally:
                db.close()

        except Exception as e:
            logger.error(f"Error sending alert notification: {e}")
    
    @monitor_background_task("stock_screening")
    async def run_stock_screening(self):
        """Run stock screening algorithms."""
        logger.info("Starting stock screening...")
        
        db = SessionLocal()
        try:
            # Get active stocks with recent data
            stocks = db.query(Stock).filter(
                Stock.is_active == True,
                Stock.current_price.isnot(None),
                Stock.last_data_update > datetime.utcnow() - timedelta(hours=1)
            ).limit(settings.MAX_STOCKS_PER_SCREEN).all()
            
            screened_count = 0
            
            for stock in stocks:
                try:
                    # Get historical data for technical analysis
                    historical_data = await self._get_historical_data_with_retry(
                        stock.symbol, period="3mo"
                    )
                    
                    if not historical_data or len(historical_data) < 50:
                        continue
                    
                    # Calculate technical indicators
                    indicators = calculate_all_indicators(historical_data)
                    
                    # Generate trading signals
                    signals = generate_trading_signals(indicators)
                    
                    # Update stock with technical indicators
                    stock.technical_indicators = {
                        'indicators': indicators,
                        'signals': signals,
                        'last_updated': datetime.utcnow().isoformat()
                    }
                    
                    screened_count += 1
                    
                except Exception as e:
                    logger.error(f"Error screening stock {stock.symbol}: {e}")
            
            db.commit()
            logger.info(f"Stock screening completed. Screened {screened_count} stocks.")

        except Exception as e:
            logger.error(f"Error in stock screening: {e}")
            db.rollback()
        finally:
            db.close()

    @monitor_background_task("data_cleanup")
    async def cleanup_old_data(self):
        """Clean up old data to maintain database performance."""
        logger.info("Starting data cleanup...")

        db = SessionLocal()
        try:
            cleanup_stats = {
                'expired_alerts': 0,
                'old_notifications': 0,
                'old_price_history': 0,
                'inactive_stocks': 0
            }

            # Clean up expired alerts
            try:
                expired_alerts = db.query(Alert).filter(
                    Alert.expires_at < datetime.utcnow(),
                    Alert.status != AlertStatus.TRIGGERED
                ).all()

                for alert in expired_alerts:
                    alert.status = AlertStatus.EXPIRED
                    cleanup_stats['expired_alerts'] += 1
            except Exception as e:
                logger.error(f"Error cleaning up expired alerts: {e}")

            # Clean up old alert notifications (older than 30 days)
            try:
                old_notifications = db.query(AlertNotification).filter(
                    AlertNotification.created_at < datetime.utcnow() - timedelta(days=30)
                )
                cleanup_stats['old_notifications'] = old_notifications.count()
                old_notifications.delete()
            except Exception as e:
                logger.error(f"Error cleaning up old notifications: {e}")

            # Clean up old price history (keep only last 2 years)
            from ..models.stock import StockPriceHistory
            old_price_history = db.query(StockPriceHistory).filter(
                StockPriceHistory.date < datetime.utcnow() - timedelta(days=730)
            )
            cleanup_stats['old_price_history'] = old_price_history.count()
            old_price_history.delete()

            # Mark inactive stocks (no price updates in 7 days)
            inactive_stocks = db.query(Stock).filter(
                Stock.last_data_update < datetime.utcnow() - timedelta(days=7),
                Stock.is_active == True
            ).all()

            for stock in inactive_stocks:
                stock.is_active = False
                cleanup_stats['inactive_stocks'] += 1

            db.commit()
            logger.info(f"Data cleanup completed: {cleanup_stats}")

        except Exception as e:
            logger.error(f"Error in data cleanup: {e}")
            db.rollback()
        finally:
            db.close()

    @monitor_background_task("cache_cleanup")
    async def cleanup_cache(self):
        """Clean up cache to free memory and remove stale data."""
        logger.info("Starting cache cleanup...")

        try:
            # Get cache statistics before cleanup
            stats_before = await cache_service.get_cache_stats()

            # Clean up old search results (they have short TTL anyway)
            await cache_service.cache_manager.delete_pattern("search:*")

            # Clean up old screening results
            await cache_service.cache_manager.delete_pattern("screening:*")

            # Clean up indicators for inactive stocks
            db = SessionLocal()
            try:
                inactive_stocks = db.query(Stock).filter(Stock.is_active == False).all()
                for stock in inactive_stocks:
                    await cache_service.invalidate_stock_cache(stock.symbol)
            finally:
                db.close()

            # Get cache statistics after cleanup
            stats_after = await cache_service.get_cache_stats()

            logger.info(f"Cache cleanup completed. Memory before: {stats_before.get('used_memory', 'unknown')}, after: {stats_after.get('used_memory', 'unknown')}")

        except Exception as e:
            logger.error(f"Error in cache cleanup: {e}")


# Global task manager instance
task_manager = BackgroundTaskManager()


async def start_background_tasks():
    """Start all background tasks."""
    await task_manager.start()


async def stop_background_tasks():
    """Stop all background tasks."""
    await task_manager.stop()


# Main function for running as a separate process
async def main():
    """Main function for background task process."""
    logger.info("Starting background task process...")
    
    try:
        await start_background_tasks()
        
        # Keep the process running
        while True:
            await asyncio.sleep(60)
            
    except KeyboardInterrupt:
        logger.info("Received interrupt signal")
    except Exception as e:
        logger.error(f"Error in background task process: {e}")
    finally:
        await stop_background_tasks()
        logger.info("Background task process stopped")


if __name__ == "__main__":
    asyncio.run(main())
