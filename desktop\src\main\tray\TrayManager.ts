import { Tray, Menu, nativeImage, app, BrowserWindow } from 'electron';
import * as path from 'path';
import { Logger } from '../../shared/Logger';

export class TrayManager {
  private logger: Logger;

  constructor() {
    this.logger = new Logger('TrayManager');
  }

  createTray(): Tray {
    const iconPath = this.getTrayIconPath();
    const trayIcon = nativeImage.createFromPath(iconPath);
    
    // Resize icon for tray (16x16 on most platforms)
    const resizedIcon = trayIcon.resize({ width: 16, height: 16 });
    
    const tray = new Tray(resizedIcon);
    
    this.setupTrayMenu(tray);
    this.setupTrayTooltip(tray);
    
    this.logger.info('System tray created');
    return tray;
  }

  private setupTrayMenu(tray: Tray): void {
    const contextMenu = Menu.buildFromTemplate([
      {
        label: 'EntryAlert',
        type: 'normal',
        enabled: false
      },
      {
        type: 'separator'
      },
      {
        label: 'Show/Hide Window',
        type: 'normal',
        click: () => {
          const mainWindow = BrowserWindow.getAllWindows()[0];
          if (mainWindow) {
            if (mainWindow.isVisible()) {
              mainWindow.hide();
            } else {
              mainWindow.show();
              mainWindow.focus();
            }
          }
        }
      },
      {
        label: 'Dashboard',
        type: 'normal',
        click: () => {
          this.showWindowAndNavigate('/dashboard');
        }
      },
      {
        label: 'Screener',
        type: 'normal',
        click: () => {
          this.showWindowAndNavigate('/screener');
        }
      },
      {
        label: 'Watchlists',
        type: 'normal',
        click: () => {
          this.showWindowAndNavigate('/watchlist');
        }
      },
      {
        type: 'separator'
      },
      {
        label: 'Market Status',
        type: 'submenu',
        submenu: [
          {
            label: 'Market Open',
            type: 'radio',
            checked: false,
            enabled: false
          },
          {
            label: 'Pre-Market',
            type: 'radio',
            checked: false,
            enabled: false
          },
          {
            label: 'After Hours',
            type: 'radio',
            checked: false,
            enabled: false
          },
          {
            label: 'Market Closed',
            type: 'radio',
            checked: true,
            enabled: false
          }
        ]
      },
      {
        label: 'Alerts',
        type: 'submenu',
        submenu: [
          {
            label: 'Enable Notifications',
            type: 'checkbox',
            checked: true,
            click: (menuItem) => {
              // Toggle notifications
              this.toggleNotifications(menuItem.checked);
            }
          },
          {
            label: 'Sound Alerts',
            type: 'checkbox',
            checked: true,
            click: (menuItem) => {
              // Toggle sound alerts
              this.toggleSoundAlerts(menuItem.checked);
            }
          },
          {
            type: 'separator'
          },
          {
            label: 'View All Alerts',
            type: 'normal',
            click: () => {
              this.showWindowAndNavigate('/alerts');
            }
          }
        ]
      },
      {
        type: 'separator'
      },
      {
        label: 'Settings',
        type: 'normal',
        click: () => {
          this.showWindowAndNavigate('/settings');
        }
      },
      {
        label: 'About',
        type: 'normal',
        click: () => {
          this.showAboutDialog();
        }
      },
      {
        type: 'separator'
      },
      {
        label: 'Quit EntryAlert',
        type: 'normal',
        click: () => {
          app.quit();
        }
      }
    ]);

    tray.setContextMenu(contextMenu);
  }

  private setupTrayTooltip(tray: Tray): void {
    tray.setToolTip('EntryAlert - Stock Screener');
  }

  updateTrayMenu(tray: Tray, marketStatus: string, alertCount: number): void {
    const contextMenu = tray.getContextMenu();
    if (contextMenu) {
      // Update market status
      const marketSubmenu = contextMenu.items.find(item => item.label === 'Market Status')?.submenu;
      if (marketSubmenu) {
        marketSubmenu.items.forEach(item => {
          item.checked = item.label === marketStatus;
        });
      }

      // Update tooltip with alert count
      const tooltip = alertCount > 0 
        ? `EntryAlert - ${alertCount} active alert${alertCount > 1 ? 's' : ''}`
        : 'EntryAlert - Stock Screener';
      tray.setToolTip(tooltip);
    }
  }

  private getTrayIconPath(): string {
    const platform = process.platform;
    let iconName: string;

    switch (platform) {
      case 'win32':
        iconName = 'tray-icon.ico';
        break;
      case 'darwin':
        iconName = 'tray-iconTemplate.png'; // macOS convention for template icons
        break;
      default:
        iconName = 'tray-icon.png';
    }

    return path.join(__dirname, '../../assets/tray', iconName);
  }

  private showWindowAndNavigate(route: string): void {
    const mainWindow = BrowserWindow.getAllWindows()[0];
    if (mainWindow) {
      mainWindow.show();
      mainWindow.focus();
      
      // Send navigation message to renderer
      mainWindow.webContents.send('navigate', route);
    }
  }

  private toggleNotifications(enabled: boolean): void {
    // Send message to main process to toggle notifications
    const mainWindow = BrowserWindow.getAllWindows()[0];
    if (mainWindow) {
      mainWindow.webContents.send('toggle-notifications', enabled);
    }
  }

  private toggleSoundAlerts(enabled: boolean): void {
    // Send message to main process to toggle sound alerts
    const mainWindow = BrowserWindow.getAllWindows()[0];
    if (mainWindow) {
      mainWindow.webContents.send('toggle-sound-alerts', enabled);
    }
  }

  private showAboutDialog(): void {
    const mainWindow = BrowserWindow.getAllWindows()[0];
    if (mainWindow) {
      mainWindow.webContents.send('show-about');
    }
  }
}
