import WebSocket from 'ws';
import { ConfigManager } from '../config/ConfigManager';
import { Logger } from '../../shared/Logger';
import { NotificationManager } from '../notifications/NotificationManager';

export interface WebSocketMessage {
  type: string;
  data: any;
  timestamp: string;
}

export class WebSocketService {
  private ws: WebSocket | null = null;
  private configManager: ConfigManager;
  private notificationManager: NotificationManager;
  private logger: Logger;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;
  private isConnecting = false;
  private shouldReconnect = true;

  constructor(configManager: ConfigManager, notificationManager: NotificationManager) {
    this.configManager = configManager;
    this.notificationManager = notificationManager;
    this.logger = new Logger('WebSocketService');
  }

  async connect(): Promise<void> {
    if (this.isConnecting || (this.ws && this.ws.readyState === WebSocket.OPEN)) {
      return;
    }

    this.isConnecting = true;
    const port = this.configManager.get('backendPort');
    const wsUrl = `ws://localhost:${port}/ws/desktop-client`;

    try {
      this.logger.info(`Connecting to WebSocket: ${wsUrl}`);
      this.ws = new WebSocket(wsUrl);

      this.ws.on('open', () => {
        this.logger.info('WebSocket connected');
        this.isConnecting = false;
        this.reconnectAttempts = 0;
        this.emitToRenderer('websocket:connected');
        
        // Subscribe to relevant topics
        this.subscribeToTopics();
      });

      this.ws.on('message', (data: WebSocket.Data) => {
        try {
          const message: WebSocketMessage = JSON.parse(data.toString());
          this.handleMessage(message);
        } catch (error) {
          this.logger.error('Failed to parse WebSocket message:', error);
        }
      });

      this.ws.on('close', (code: number, reason: string) => {
        this.logger.warn(`WebSocket closed: ${code} - ${reason}`);
        this.isConnecting = false;
        this.ws = null;
        this.emitToRenderer('websocket:disconnected', { code, reason });
        
        if (this.shouldReconnect) {
          this.scheduleReconnect();
        }
      });

      this.ws.on('error', (error: Error) => {
        this.logger.error('WebSocket error:', error);
        this.isConnecting = false;
        this.emitToRenderer('websocket:error', error.message);
      });

    } catch (error) {
      this.logger.error('Failed to create WebSocket connection:', error);
      this.isConnecting = false;
      throw error;
    }
  }

  disconnect(): void {
    this.shouldReconnect = false;
    
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
    
    this.logger.info('WebSocket disconnected');
  }

  send(message: any): void {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message));
    } else {
      this.logger.warn('Cannot send message: WebSocket not connected');
    }
  }

  private subscribeToTopics(): void {
    // Subscribe to stock updates
    this.send({
      type: 'subscribe',
      topic: 'stock:*'
    });

    // Subscribe to market status updates
    this.send({
      type: 'subscribe',
      topic: 'market:status'
    });

    // Subscribe to alert notifications
    this.send({
      type: 'subscribe',
      topic: 'alerts:*'
    });

    // Subscribe to system notifications
    this.send({
      type: 'subscribe',
      topic: 'system:*'
    });
  }

  private handleMessage(message: WebSocketMessage): void {
    this.logger.debug(`WebSocket message received: ${message.type}`);

    switch (message.type) {
      case 'stock_update':
        this.handleStockUpdate(message.data);
        break;
        
      case 'market_status':
        this.handleMarketStatusUpdate(message.data);
        break;
        
      case 'alert_triggered':
        this.handleAlertTriggered(message.data);
        break;
        
      case 'system_notification':
        this.handleSystemNotification(message.data);
        break;
        
      case 'watchlist_update':
        this.handleWatchlistUpdate(message.data);
        break;
        
      case 'screener_result':
        this.handleScreenerResult(message.data);
        break;
        
      default:
        this.logger.debug(`Unhandled message type: ${message.type}`);
    }

    // Forward all messages to renderer process
    this.emitToRenderer('websocket:message', message);
  }

  private handleStockUpdate(data: any): void {
    // Update local cache if needed
    this.emitToRenderer('stock:update', data);
  }

  private handleMarketStatusUpdate(data: any): void {
    this.logger.info(`Market status update: ${data.status}`);
    
    // Show notification for market status changes
    if (this.configManager.get('enableNotifications')) {
      this.notificationManager.showMarketStatusNotification(data.status);
    }
    
    this.emitToRenderer('market:status-update', data);
  }

  private handleAlertTriggered(data: any): void {
    this.logger.info(`Alert triggered: ${data.symbol} - ${data.alertType}`);
    
    // Show desktop notification
    if (this.configManager.get('enableNotifications')) {
      this.notificationManager.showStockAlert(
        data.symbol,
        data.price,
        data.change,
        data.alertType
      );
    }
    
    this.emitToRenderer('alert:triggered', data);
  }

  private handleSystemNotification(data: any): void {
    this.logger.info(`System notification: ${data.title}`);
    
    if (this.configManager.get('enableNotifications')) {
      this.notificationManager.showSystemNotification(
        data.title,
        data.message,
        data.type || 'info'
      );
    }
    
    this.emitToRenderer('system:notification', data);
  }

  private handleWatchlistUpdate(data: any): void {
    this.logger.debug(`Watchlist update: ${data.watchlistId}`);
    this.emitToRenderer('watchlist:update', data);
  }

  private handleScreenerResult(data: any): void {
    this.logger.debug(`Screener result: ${data.screenId}`);
    this.emitToRenderer('screener:result', data);
  }

  private scheduleReconnect(): void {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      this.logger.error('Max reconnection attempts reached');
      this.emitToRenderer('websocket:max-reconnect-attempts');
      return;
    }

    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts);
    this.reconnectAttempts++;

    this.logger.info(`Scheduling reconnection attempt ${this.reconnectAttempts} in ${delay}ms`);

    setTimeout(() => {
      if (this.shouldReconnect) {
        this.connect().catch(error => {
          this.logger.error('Reconnection failed:', error);
        });
      }
    }, delay);
  }

  private emitToRenderer(channel: string, data?: any): void {
    const { BrowserWindow } = require('electron');
    const mainWindow = BrowserWindow.getAllWindows()[0];
    if (mainWindow) {
      mainWindow.webContents.send(channel, data);
    }
  }

  // Public methods for managing connection
  isConnected(): boolean {
    return this.ws !== null && this.ws.readyState === WebSocket.OPEN;
  }

  getConnectionState(): string {
    if (!this.ws) return 'disconnected';
    
    switch (this.ws.readyState) {
      case WebSocket.CONNECTING:
        return 'connecting';
      case WebSocket.OPEN:
        return 'connected';
      case WebSocket.CLOSING:
        return 'closing';
      case WebSocket.CLOSED:
        return 'closed';
      default:
        return 'unknown';
    }
  }

  // Subscribe to specific stock updates
  subscribeToStock(symbol: string): void {
    this.send({
      type: 'subscribe',
      topic: `stock:${symbol}`
    });
  }

  // Unsubscribe from specific stock updates
  unsubscribeFromStock(symbol: string): void {
    this.send({
      type: 'unsubscribe',
      topic: `stock:${symbol}`
    });
  }

  // Subscribe to watchlist updates
  subscribeToWatchlist(watchlistId: number): void {
    this.send({
      type: 'subscribe',
      topic: `watchlist:${watchlistId}`
    });
  }

  // Unsubscribe from watchlist updates
  unsubscribeFromWatchlist(watchlistId: number): void {
    this.send({
      type: 'unsubscribe',
      topic: `watchlist:${watchlistId}`
    });
  }
}
