import React, { useState, useEffect } from 'react';
import { 
  Setting<PERSON>, 
  Bell, 
  Volume2, 
  VolumeX, 
  Monitor, 
  Minimize, 
  X,
  Power,
  RefreshCw,
  Save,
  RotateCcw
} from 'lucide-react';
import { useElectronConfig, useBackendStatus, useElectronNotifications } from '../hooks/useElectron';

export const DesktopSettings: React.FC = () => {
  const { config, setConfig, resetConfig } = useElectronConfig();
  const { status: backendStatus, restart: restartBackend } = useBackendStatus();
  const { showSystemNotification } = useElectronNotifications();
  
  const [localConfig, setLocalConfig] = useState<any>({});
  const [hasChanges, setHasChanges] = useState(false);
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    setLocalConfig(config);
  }, [config]);

  const handleConfigChange = (key: string, value: any) => {
    setLocalConfig((prev: any) => ({
      ...prev,
      [key]: value
    }));
    setHasChanges(true);
  };

  const handleSave = async () => {
    setSaving(true);
    try {
      for (const [key, value] of Object.entries(localConfig)) {
        await setConfig(key, value);
      }
      setHasChanges(false);
      showSystemNotification('Settings Saved', 'Your preferences have been saved successfully.', 'info');
    } catch (error) {
      showSystemNotification('Save Error', 'Failed to save settings. Please try again.', 'error');
    } finally {
      setSaving(false);
    }
  };

  const handleReset = async () => {
    if (window.confirm('Are you sure you want to reset all settings to defaults?')) {
      await resetConfig();
      setHasChanges(false);
      showSystemNotification('Settings Reset', 'All settings have been reset to defaults.', 'info');
    }
  };

  const handleRestartBackend = async () => {
    try {
      await restartBackend();
      showSystemNotification('Backend Restarted', 'The backend server has been restarted successfully.', 'info');
    } catch (error) {
      showSystemNotification('Restart Failed', 'Failed to restart the backend server.', 'error');
    }
  };

  return (
    <div className="p-6 bg-gray-900 text-white">
      <div className="max-w-4xl mx-auto">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            <Settings className="w-6 h-6 text-blue-400" />
            <h1 className="text-2xl font-bold">Desktop Settings</h1>
          </div>
          
          <div className="flex items-center space-x-2">
            {hasChanges && (
              <button
                onClick={handleSave}
                disabled={saving}
                className="flex items-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors disabled:opacity-50"
              >
                <Save size={16} />
                <span>{saving ? 'Saving...' : 'Save Changes'}</span>
              </button>
            )}
            
            <button
              onClick={handleReset}
              className="flex items-center space-x-2 px-4 py-2 bg-gray-600 hover:bg-gray-700 rounded-lg transition-colors"
            >
              <RotateCcw size={16} />
              <span>Reset</span>
            </button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Window Behavior */}
          <div className="bg-gray-800 rounded-lg p-6">
            <h2 className="text-lg font-semibold mb-4 flex items-center space-x-2">
              <Monitor className="w-5 h-5 text-blue-400" />
              <span>Window Behavior</span>
            </h2>
            
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium">Minimize to System Tray</label>
                  <p className="text-xs text-gray-400">Hide window in system tray when minimized</p>
                </div>
                <input
                  type="checkbox"
                  checked={localConfig.minimizeToTray || false}
                  onChange={(e) => handleConfigChange('minimizeToTray', e.target.checked)}
                  className="w-4 h-4 text-blue-600 bg-gray-700 border-gray-600 rounded focus:ring-blue-500"
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium">Close to System Tray</label>
                  <p className="text-xs text-gray-400">Hide window in system tray when closed</p>
                </div>
                <input
                  type="checkbox"
                  checked={localConfig.closeToTray || false}
                  onChange={(e) => handleConfigChange('closeToTray', e.target.checked)}
                  className="w-4 h-4 text-blue-600 bg-gray-700 border-gray-600 rounded focus:ring-blue-500"
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium">Start Minimized</label>
                  <p className="text-xs text-gray-400">Start the application minimized</p>
                </div>
                <input
                  type="checkbox"
                  checked={localConfig.startMinimized || false}
                  onChange={(e) => handleConfigChange('startMinimized', e.target.checked)}
                  className="w-4 h-4 text-blue-600 bg-gray-700 border-gray-600 rounded focus:ring-blue-500"
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium">Auto Start</label>
                  <p className="text-xs text-gray-400">Start with system boot</p>
                </div>
                <input
                  type="checkbox"
                  checked={localConfig.autoStart || false}
                  onChange={(e) => handleConfigChange('autoStart', e.target.checked)}
                  className="w-4 h-4 text-blue-600 bg-gray-700 border-gray-600 rounded focus:ring-blue-500"
                />
              </div>
            </div>
          </div>

          {/* Notifications */}
          <div className="bg-gray-800 rounded-lg p-6">
            <h2 className="text-lg font-semibold mb-4 flex items-center space-x-2">
              <Bell className="w-5 h-5 text-blue-400" />
              <span>Notifications</span>
            </h2>
            
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium">Enable Notifications</label>
                  <p className="text-xs text-gray-400">Show desktop notifications for alerts</p>
                </div>
                <input
                  type="checkbox"
                  checked={localConfig.enableNotifications || false}
                  onChange={(e) => handleConfigChange('enableNotifications', e.target.checked)}
                  className="w-4 h-4 text-blue-600 bg-gray-700 border-gray-600 rounded focus:ring-blue-500"
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium">Sound Alerts</label>
                  <p className="text-xs text-gray-400">Play sound with notifications</p>
                </div>
                <input
                  type="checkbox"
                  checked={localConfig.enableSoundAlerts || false}
                  onChange={(e) => handleConfigChange('enableSoundAlerts', e.target.checked)}
                  className="w-4 h-4 text-blue-600 bg-gray-700 border-gray-600 rounded focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="text-sm font-medium block mb-2">Notification Duration (seconds)</label>
                <input
                  type="range"
                  min="1"
                  max="30"
                  value={(localConfig.notificationDuration || 10000) / 1000}
                  onChange={(e) => handleConfigChange('notificationDuration', parseInt(e.target.value) * 1000)}
                  className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer"
                />
                <div className="flex justify-between text-xs text-gray-400 mt-1">
                  <span>1s</span>
                  <span>{((localConfig.notificationDuration || 10000) / 1000)}s</span>
                  <span>30s</span>
                </div>
              </div>
            </div>
          </div>

          {/* Backend Configuration */}
          <div className="bg-gray-800 rounded-lg p-6">
            <h2 className="text-lg font-semibold mb-4 flex items-center space-x-2">
              <Power className="w-5 h-5 text-blue-400" />
              <span>Backend Server</span>
            </h2>
            
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium block mb-2">Server Port</label>
                <input
                  type="number"
                  min="1024"
                  max="65535"
                  value={localConfig.backendPort || 8000}
                  onChange={(e) => handleConfigChange('backendPort', parseInt(e.target.value))}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div className="flex items-center justify-between p-3 bg-gray-700 rounded-lg">
                <div>
                  <div className="text-sm font-medium">Status</div>
                  <div className={`text-xs ${backendStatus.running ? 'text-green-400' : 'text-red-400'}`}>
                    {backendStatus.running ? 'Running' : 'Stopped'}
                    {backendStatus.running && ` (Port ${backendStatus.port})`}
                  </div>
                </div>
                <button
                  onClick={handleRestartBackend}
                  className="flex items-center space-x-2 px-3 py-1 bg-blue-600 hover:bg-blue-700 rounded transition-colors"
                >
                  <RefreshCw size={14} />
                  <span>Restart</span>
                </button>
              </div>
            </div>
          </div>

          {/* Performance */}
          <div className="bg-gray-800 rounded-lg p-6">
            <h2 className="text-lg font-semibold mb-4 flex items-center space-x-2">
              <Monitor className="w-5 h-5 text-blue-400" />
              <span>Performance</span>
            </h2>
            
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium">Hardware Acceleration</label>
                  <p className="text-xs text-gray-400">Use GPU acceleration for better performance</p>
                </div>
                <input
                  type="checkbox"
                  checked={localConfig.enableHardwareAcceleration || false}
                  onChange={(e) => handleConfigChange('enableHardwareAcceleration', e.target.checked)}
                  className="w-4 h-4 text-blue-600 bg-gray-700 border-gray-600 rounded focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="text-sm font-medium block mb-2">Max Memory Usage (MB)</label>
                <input
                  type="range"
                  min="128"
                  max="4096"
                  step="128"
                  value={localConfig.maxMemoryUsage || 512}
                  onChange={(e) => handleConfigChange('maxMemoryUsage', parseInt(e.target.value))}
                  className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer"
                />
                <div className="flex justify-between text-xs text-gray-400 mt-1">
                  <span>128MB</span>
                  <span>{localConfig.maxMemoryUsage || 512}MB</span>
                  <span>4GB</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {hasChanges && (
          <div className="mt-6 p-4 bg-yellow-900/20 border border-yellow-600 rounded-lg">
            <p className="text-yellow-400 text-sm">
              You have unsaved changes. Click "Save Changes" to apply them.
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default DesktopSettings;
