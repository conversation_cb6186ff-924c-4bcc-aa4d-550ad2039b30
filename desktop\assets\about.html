<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>About EntryAlert</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #1a1a1a;
            color: white;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
        }

        .container {
            max-width: 400px;
            margin: 0 auto;
            text-align: center;
        }

        .logo {
            width: 64px;
            height: 64px;
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);
        }

        .logo::before {
            content: "📊";
            font-size: 24px;
        }

        .title {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 8px;
            background: linear-gradient(135deg, #3b82f6 0%, #60a5fa 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .version {
            font-size: 14px;
            color: #9ca3af;
            margin-bottom: 20px;
        }

        .description {
            font-size: 14px;
            color: #d1d5db;
            margin-bottom: 30px;
            text-align: left;
        }

        .info-grid {
            display: grid;
            gap: 12px;
            text-align: left;
            margin-bottom: 30px;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 12px;
            background: #2d2d2d;
            border-radius: 6px;
            font-size: 12px;
        }

        .info-label {
            color: #9ca3af;
        }

        .info-value {
            color: #d1d5db;
            font-weight: 500;
        }

        .links {
            display: flex;
            gap: 16px;
            justify-content: center;
            margin-bottom: 20px;
        }

        .link {
            color: #3b82f6;
            text-decoration: none;
            font-size: 12px;
            padding: 6px 12px;
            border: 1px solid #3b82f6;
            border-radius: 4px;
            transition: all 0.2s;
        }

        .link:hover {
            background: #3b82f6;
            color: white;
        }

        .copyright {
            font-size: 11px;
            color: #6b7280;
            margin-top: 20px;
        }

        .tech-stack {
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #374151;
        }

        .tech-title {
            font-size: 12px;
            color: #9ca3af;
            margin-bottom: 8px;
            font-weight: 600;
        }

        .tech-list {
            font-size: 11px;
            color: #6b7280;
            line-height: 1.4;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo"></div>
        <div class="title">EntryAlert</div>
        <div class="version">Version 1.0.0</div>
        
        <div class="description">
            A comprehensive US stock screener application that processes 8000+ stocks in real-time, 
            providing intelligent watchlist notifications and entry point alerts.
        </div>

        <div class="info-grid">
            <div class="info-item">
                <span class="info-label">Platform:</span>
                <span class="info-value" id="platform">Desktop</span>
            </div>
            <div class="info-item">
                <span class="info-label">Backend:</span>
                <span class="info-value">FastAPI + Python</span>
            </div>
            <div class="info-item">
                <span class="info-label">Frontend:</span>
                <span class="info-value">React + TypeScript</span>
            </div>
            <div class="info-item">
                <span class="info-label">Desktop:</span>
                <span class="info-value">Electron</span>
            </div>
        </div>

        <div class="links">
            <a href="#" class="link" onclick="openExternal('https://github.com/entryalert/entryalert')">GitHub</a>
            <a href="#" class="link" onclick="openExternal('https://entryalert.com/docs')">Documentation</a>
            <a href="#" class="link" onclick="openExternal('https://entryalert.com/support')">Support</a>
        </div>

        <div class="tech-stack">
            <div class="tech-title">Technology Stack</div>
            <div class="tech-list">
                Backend: FastAPI, SQLAlchemy, PostgreSQL, Redis, APScheduler<br>
                Frontend: React 18, TypeScript, Tailwind CSS, Chart.js<br>
                Desktop: Electron, Node.js<br>
                APIs: Alpha Vantage, Yahoo Finance
            </div>
        </div>

        <div class="copyright">
            © 2024 EntryAlert. All rights reserved.
        </div>
    </div>

    <script>
        function openExternal(url) {
            if (window.electronAPI) {
                window.electronAPI.system.openExternal(url);
            } else {
                window.open(url, '_blank');
            }
        }

        // Update platform info if available
        if (window.electronAPI) {
            const platform = window.electronAPI.app.getPlatform();
            const platformNames = {
                'win32': 'Windows',
                'darwin': 'macOS',
                'linux': 'Linux'
            };
            document.getElementById('platform').textContent = platformNames[platform] || platform;
        }
    </script>
</body>
</html>
